import { useChat } from "@ai-sdk/react";
import type { Attachment } from "ai";
import { Ai, useStore } from "cargo-api";
import classNames from "classnames";
import { config } from "config";
import { ApiContext } from "contexts/ApiContext";
import { AuthContext } from "contexts/AuthContext";
import { NotificationContext } from "contexts/NotificationContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import React from "react";
import { useQuery } from "react-query";
import { v4 as uuidv4 } from "uuid";

import { ChatArtifact } from "./ChatArtifact/ChatArtifact";
import { ChatInput } from "./ChatInput/ChatInput";
import { ChatMessages } from "./ChatMessages/ChatMessages";
import { reshapeMessage } from "./utils";

type Props = {
  className?: string;
  agent: Ai.Agent;
  release: Ai.Release;
  chatUuid?: string;
  isDisabled?: boolean;
  inputToolbar?: React.ReactNode;
};

export const Chat: React.FC<Props> = (props) => {
  const {
    className,
    agent,
    release,
    chatUuid,
    isDisabled = false,
    inputToolbar,
  } = props;

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);
  const { getAccessToken } = React.useContext(AuthContext);
  const { workspaceUuid } = React.useContext(WorkspaceContext);

  const tokens = useStore((state) => state.tokens);

  const [attachments, setAttachments] = React.useState<Attachment[]>([]);

  const voteListPayload: Ai.Api.ListVotesPayload = React.useMemo(() => {
    return {
      chatUuid: chatUuid as string,
    };
  }, [chatUuid]);

  const voteListState = useQuery(
    Ai.voteKeys.list(workspaceUuid, voteListPayload),
    () => api.ai.vote.list(voteListPayload),
    {
      enabled: chatUuid !== undefined,
    },
  );

  const messageListPayload: Ai.Api.ListMessagesPayload = React.useMemo(() => {
    return {
      chatUuid: chatUuid as string,
    };
  }, [chatUuid]);

  const messageListState = useQuery(
    Ai.messageKeys.list(workspaceUuid, messageListPayload),
    () => api.ai.message.list(messageListPayload),
    {
      enabled: chatUuid !== undefined,
      refetchOnWindowFocus: false,
      onSuccess: ({ messages }) => {
        if (token === undefined) {
          return;
        }

        setMessages(
          messages.map((message) => {
            return reshapeMessage(message, token);
          }),
        );
      },
    },
  );

  const votes = React.useMemo(() => {
    if (voteListState.isSuccess === false) {
      return [];
    }

    return voteListState.data.votes;
  }, [voteListState.data, voteListState.isSuccess]);

  const token = React.useMemo<string | undefined>(() => {
    const token = tokens.find((token) => {
      return token.userUuid === undefined;
    });

    if (token === undefined) {
      return undefined;
    }

    return token.token;
  }, [tokens]);

  const handleFetch = React.useCallback(
    async (resource: RequestInfo | URL, options?: RequestInit) => {
      const headers = options === undefined ? {} : options.headers;
      const parsedBody = JSON.parse(
        options !== undefined ? (options.body as string) : "{}",
      );

      const lastMessage = parsedBody["messages"].at(-1);
      const { experimental_attachments: lastMessageAttachments = [] } =
        lastMessage;

      const result = await fetch(resource, {
        ...options,
        body: JSON.stringify({
          chatUuid,
          text: lastMessage.content,
          uuid: lastMessage.id,
          attachments: lastMessageAttachments.map((attachment: Attachment) => {
            return {
              name: attachment.name,
              contentType: attachment.contentType,
              s3Filename: new URL(attachment.url).searchParams.get(
                "s3Filename",
              ),
            };
          }),
        }),
        headers: {
          ...headers,
          Authorization: `Bearer ${await getAccessToken()}`,
          "selected-workspace-uuid": workspaceUuid,
        },
      });

      return result;
    },
    [getAccessToken, workspaceUuid, chatUuid],
  );

  // @aureeaubert - scroll message
  // @aureeaubert - validation
  const {
    messages,
    setMessages,
    input,
    setInput,
    reload,
    handleSubmit,
    append,
    status,
    stop,
  } = useChat({
    api: `${config.api.baseUrl}/v1/ai/messages/stream`,
    fetch: handleFetch,
    generateId: uuidv4,
    sendExtraMessageFields: true,
    onError: (error) => {
      addNotification({
        title: error instanceof Error ? error.message : "Unknown error",
        type: "error",
      });
    },
  });

  return (
    <div className={classNames("flex flex-col", className)}>
      {messageListState.isSuccess === true && chatUuid !== undefined ? (
        <ChatMessages
          chatUuid={chatUuid}
          status={status}
          votes={votes}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isDisabled === true}
          agent={agent}
          release={release}
        />
      ) : (
        <div className="min-h-0 flex-1"></div>
      )}

      <form className="mx-auto flex w-full max-w-3xl space-y-2 px-4 pb-4">
        {isDisabled === false ? (
          <ChatInput
            release={release}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            status={status}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
            append={append}
            toolbar={inputToolbar}
          />
        ) : null}
      </form>
      {chatUuid !== undefined ? (
        <ChatArtifact
          agent={agent}
          release={release}
          chatUuid={chatUuid}
          input={input}
          setInput={setInput}
          handleSubmit={handleSubmit}
          status={status}
          stop={stop}
          attachments={attachments}
          setAttachments={setAttachments}
          append={append}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          votes={votes}
          isReadonly={isDisabled === true}
        />
      ) : null}
    </div>
  );
};
