import File06 from "@untitled-ui/icons-react/build/esm/File06";
import type { Attachment } from "ai";
import { Spin } from "cargo-components/Spin";

type Props = {
  attachment: Attachment;
  isUploading?: boolean;
};

export const ChatPreviewAttachment: React.FC<Props> = (props) => {
  const { attachment, isUploading = false } = props;
  const { name, url, contentType } = attachment;

  return (
    <div className="flex flex-col space-y-2">
      <div className="relative flex aspect-video h-16 w-20 flex-col items-center justify-center rounded-md bg-neutral-100 dark:bg-neutral-800">
        {contentType !== undefined &&
        contentType.startsWith("image") === true ? (
          // NOTE: it is recommended to use next/image for images
          // eslint-disable-next-line @next/next/no-img-element
          <img
            key={url}
            src={url}
            alt={name !== undefined ? name : "An image attachment"}
            className="size-full rounded-md object-cover"
          />
        ) : isUploading === false ? (
          <File06 className="size-6" />
        ) : (
          <div className=""></div>
        )}

        {isUploading === true ? (
          <div className="absolute text-neutral-500">
            <Spin />
          </div>
        ) : null}
      </div>
      <div className="max-w-16 truncate text-xs text-neutral-500">{name}</div>
    </div>
  );
};
