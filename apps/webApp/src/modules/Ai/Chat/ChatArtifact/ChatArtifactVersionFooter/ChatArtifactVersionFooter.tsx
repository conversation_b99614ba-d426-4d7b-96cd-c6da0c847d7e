"use client";

import type { Ai } from "cargo-api";
import { Button } from "cargo-components/Button";
import { ApiContext } from "contexts/ApiContext";
import { motion } from "framer-motion";
import React from "react";
import { useMutation } from "react-query";

type Props = {
  handleVersionChange: (type: "next" | "prev" | "toggle" | "latest") => void;
  documents: Ai.Document[];
  onReset: (documents: Ai.Document[]) => void;
  currentVersionIndex: number;
};

export const ChatArtifactVersionFooter: React.FC<Props> = (props) => {
  const { handleVersionChange, documents, onReset, currentVersionIndex } =
    props;

  const { api } = React.useContext(ApiContext);

  const currentDocument = React.useMemo(() => {
    return documents[currentVersionIndex];
  }, [currentVersionIndex, documents]);

  const resetDocumentState = useMutation(api.ai.document.reset, {
    onSuccess({ document }) {
      const filteredDocuments = documents.filter(
        (doc) => new Date(doc.createdAt) < new Date(document.createdAt),
      );

      onReset(filteredDocuments);
    },
  });

  return (
    <motion.div
      className="z-50 absolute bottom-0 flex w-full flex-col justify-between gap-4 border-t p-4 lg:flex-row"
      initial={{ y: 77 }}
      animate={{ y: 0 }}
      exit={{ y: 77 }}
      transition={{ type: "spring", stiffness: 140, damping: 20 }}
    >
      <div>
        <div>You are viewing a previous version</div>
        <div className="text-sm">Restore this version to make edits</div>
      </div>

      <div className="flex flex-row gap-4">
        <Button
          text="Restore this version"
          isLoading={resetDocumentState.isLoading}
          onClick={async () => {
            if (currentDocument === undefined) {
              return;
            }

            resetDocumentState.mutate({ uuid: currentDocument.uuid });
          }}
        />
        <Button
          type="secondary"
          onClick={() => {
            handleVersionChange("latest");
          }}
          text="Back to latest version"
        />
      </div>
    </motion.div>
  );
};
