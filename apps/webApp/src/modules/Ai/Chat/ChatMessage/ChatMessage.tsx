"use client";

import type { UseChatHelpers } from "@ai-sdk/react";
import { FiEdit } from "@react-icons/all-files/fi/FiEdit";
import type { UIMessage } from "ai";
import type { Ai } from "cargo-api";
import { Button } from "cargo-components/Button";
import { equals } from "cargo-utils";
import classNames from "classnames";
import { LazyMarkdown } from "components/LazyMarkdown";
import { LazyTooltip } from "components/LazyTooltip";
import { AnimatePresence, motion } from "framer-motion";
import { AgentIcon } from "modules/Ai/AgentIcon";
import React, { useState } from "react";

import { ChatPreviewAttachment } from "../ChatPreviewAttachment/ChatPreviewAttachment";
import { ChatToolInvocation } from "../ChatToolInvocation/ChatToolInvocation";
import { sanitizeText } from "../utils";
import { ChatMessageActions } from "./ChatMessageActions/ChatMessageActions";
import { ChatMessageEditor } from "./ChatMessageEditor/ChatMessageEditor";
import { ChatMessageReasoning } from "./ChatMessageReasoning/ChatMessageReasoning";

type Props = {
  agent: Ai.Agent;
  release: Ai.Release;
  chatUuid: string;
  message: UIMessage;
  votes: Ai.Vote[];
  isLoading: boolean;
  setMessages: UseChatHelpers["setMessages"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
  requiresScrollPadding: boolean;
};

const PureChatMessage: React.FC<Props> = (props) => {
  const [mode, setMode] = useState<"view" | "edit">("view");
  const {
    agent,
    release,
    chatUuid,
    message,
    votes,
    isLoading,
    setMessages,
    reload,
    isReadonly,
    requiresScrollPadding,
  } = props;

  return (
    <AnimatePresence>
      <motion.div
        className="group/message mx-auto w-full max-w-3xl px-4"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={classNames(
            "flex w-full flex-row space-x-4 group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl",
            mode === "edit" ? "w-full" : "group-data-[role=user]/message:w-fit",
          )}
        >
          {message.role === "assistant" ? (
            <AgentIcon className="shrink-0" agent={agent} />
          ) : null}

          <div
            className={classNames(
              "flex min-w-0 flex-1 flex-col space-y-4",
              message.role === "assistant" && requiresScrollPadding === true
                ? "min-h-96"
                : null,
            )}
          >
            {message.experimental_attachments !== undefined &&
              message.experimental_attachments.length > 0 && (
                <div className="flex flex-row justify-end space-x-2">
                  {message.experimental_attachments.map((attachment) => (
                    <ChatPreviewAttachment
                      key={attachment.url}
                      attachment={attachment}
                    />
                  ))}
                </div>
              )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === "reasoning") {
                return (
                  <ChatMessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.reasoning}
                  />
                );
              }

              if (type === "text") {
                if (mode === "view") {
                  return (
                    <div key={key} className="flex flex-row items-start gap-2">
                      {message.role === "user" && isReadonly === false && (
                        <LazyTooltip text="Edit message">
                          <Button
                            type="white"
                            className="opacity-0 group-hover/message:opacity-100"
                            onClick={() => {
                              setMode("edit");
                            }}
                            icon={<FiEdit />}
                          />
                        </LazyTooltip>
                      )}

                      <div
                        className={classNames(
                          "flex flex-col space-y-4",
                          message.role === "user"
                            ? "rounded-xl bg-emerald-600 px-3 py-2 text-white"
                            : null,
                        )}
                      >
                        <LazyMarkdown>{sanitizeText(part.text)}</LazyMarkdown>
                      </div>
                    </div>
                  );
                }

                if (mode === "edit") {
                  return (
                    <div key={key} className="flex flex-row items-start gap-2">
                      <div className="size-8" />

                      <ChatMessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                      />
                    </div>
                  );
                }
              }

              if (type === "tool-invocation") {
                return (
                  <ChatToolInvocation
                    key={key}
                    release={release}
                    toolInvocation={part.toolInvocation}
                  />
                );
              }

              return null;
            })}

            {isReadonly === false ? (
              <ChatMessageActions
                key={`action-${message.id}`}
                chatUuid={chatUuid}
                message={message}
                votes={votes}
                isLoading={isLoading}
              />
            ) : null}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const ChatMessage = React.memo(
  PureChatMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (prevProps.requiresScrollPadding !== nextProps.requiresScrollPadding)
      return false;
    if (equals(prevProps.message.parts, nextProps.message.parts) === false)
      return false;
    if (equals(prevProps.votes, nextProps.votes) === false) return false;

    return true;
  },
);
