import type { Ai, Connection, Orchestration } from "cargo-api";
import type { JsonSchema } from "cargo-utils";
import { reshapeReleaseFormFieldsToSchemas } from "modules/Orchestration/utils/reshapeReleaseFormFieldsToSchemas";

type GetNodeFormSchemasPayload = {
  integrations: Connection.Integration[];
  workflows: Orchestration.Workflow[];
  orchestrationTools: Orchestration.Tool[];
  tool: Ai.Tool;
};
type GetNodeFormSchemasResult = {
  jsonSchema: JsonSchema;
  uiSchema: Record<string, unknown>;
};

export const getToolFormSchemas = (
  payload: GetNodeFormSchemasPayload,
): GetNodeFormSchemasResult => {
  const { integrations, workflows, tool } = payload;

  if (tool.kind === "connector") {
    const integration = integrations.find((integration) => {
      return integration.slug === tool.integrationSlug;
    });

    if (integration === undefined) {
      return { jsonSchema: {}, uiSchema: {} };
    }

    const action = integration.actions[tool.actionSlug];

    if (action === undefined) {
      return { jsonSchema: {}, uiSchema: {} };
    }

    return action.config;
  }

  if (tool.kind === "tool") {
    if (tool.toolUuid === undefined) {
      return { jsonSchema: {}, uiSchema: {} };
    }

    const workflow = workflows.find(
      (workflow) => workflow.toolUuid === tool.toolUuid,
    );

    if (
      workflow === undefined ||
      workflow.deployedRelease === undefined ||
      workflow.deployedRelease.formFields === undefined
    ) {
      return { jsonSchema: {}, uiSchema: {} };
    }

    return reshapeReleaseFormFieldsToSchemas(
      workflow.deployedRelease.formFields,
      true,
    );
  }

  return { jsonSchema: {}, uiSchema: {} };
};

type GetComputedNodeFormSchemasPayload = {
  integrations: Connection.Integration[];
  workflows: Orchestration.Workflow[];
  orchestrationTools: Orchestration.Tool[];
  tool: Ai.Tool;
};
type GetComputedNodeFormSchemasResult = {
  jsonSchema: JsonSchema;
  uiSchema: Record<string, unknown>;
};

export const getComputedToolFormSchemas = (
  payload: GetComputedNodeFormSchemasPayload,
): GetComputedNodeFormSchemasResult => {
  const { uiSchema, jsonSchema } = getToolFormSchemas(payload);

  return {
    uiSchema,
    jsonSchema: {
      ...jsonSchema,
      definitions: {
        ...jsonSchema.definitions,
        expression: {
          oneOf: [{}],
        },
        templateExpression: {
          oneOf: [{}],
        },
      },
    },
  };
};
