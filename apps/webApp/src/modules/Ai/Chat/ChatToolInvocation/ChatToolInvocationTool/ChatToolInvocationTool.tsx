import CheckCircle from "@untitled-ui/icons-react/build/esm/CheckCircle";
import ChevronDown from "@untitled-ui/icons-react/build/esm/ChevronDown";
import ChevronUp from "@untitled-ui/icons-react/build/esm/ChevronUp";
import { type Ai, useStore } from "cargo-api";
import { Badge } from "cargo-components/Badge";
import { Button } from "cargo-components/Button";
import { InputSearch } from "cargo-components/InputSearch";
import { JsonViewer } from "cargo-components/JsonViewer";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Spin } from "cargo-components/Spin";
import { Title } from "cargo-components/Title";
import { Form, useForm } from "components/Form";
import { DynamicSchemaWidget } from "components/Form/widgets/DynamicSchemaWidget";
import { IntegrationAutocompleteWidget } from "components/Form/widgets/IntegrationAutocompleteWidget";
import { IntegrationFilterWidget } from "components/Form/widgets/IntegrationFilterWidget";
import { IntegrationSortWidget } from "components/Form/widgets/IntegrationSortWidget";
import { ToolIcon } from "modules/Ai/ToolIcon";
import { ComputedExpressionWidget } from "modules/Orchestration/NodeInput/formWidgets";
import React from "react";

import { getComputedToolFormSchemas } from "./form";

type Props = {
  tool: Ai.Tool;
  args: Record<string, unknown>;
  result: Record<string, unknown>;
};

export const ChatToolInvocationTool: React.FC<Props> = (props) => {
  const { tool, args, result } = props;

  const integrations = useStore((state) => state.integrations);
  const orchestrationTools = useStore((state) => state.tools);
  const workflows = useStore((state) => state.workflows);

  const [isExpanded, setIsExpanded] = React.useState<boolean>(false);

  const { uiSchema, jsonSchema } = React.useMemo(() => {
    return getComputedToolFormSchemas({
      integrations,
      orchestrationTools,
      workflows,
      tool,
    });
  }, [integrations, tool, orchestrationTools, workflows]);

  const toolConnectorUuid =
    tool.kind === "connector" ? tool.connectorUuid : undefined;

  const formContext = React.useMemo(() => {
    return {
      connectorUuid: toolConnectorUuid,
    };
  }, [toolConnectorUuid]);

  const form = useForm<Record<string, unknown>>({
    initialData: args,
  });

  const widgets = React.useMemo(() => {
    return {
      ExpressionWidget: ComputedExpressionWidget,
      TemplateExpressionWidget: ComputedExpressionWidget,
      IntegrationAutocompleteWidget,
      IntegrationFilterWidget,
      IntegrationSortWidget,
      DynamicSchemaWidget,
    };
  }, []);

  const [searchValue, setSearchValue] = React.useState<string | undefined>(
    undefined,
  );

  return (
    <div className="rounded-md border border-neutral-300/50 dark:border-neutral-700/50">
      <ScreenBody
        title={
          <div className="flex flex-row items-center space-x-2">
            <ToolIcon tool={tool} />
            <Title level={3}>{tool.name}</Title>
          </div>
        }
        className="max-h-[calc(50dvh)] overflow-auto p-2"
        header={
          <div className="flex flex-row space-x-2">
            {result !== undefined ? (
              <Badge
                variant="light"
                color="green"
                className="flex flex-row items-center space-x-1"
              >
                <CheckCircle className="size-4" />
                <span>Success</span>
              </Badge>
            ) : (
              <Badge
                variant="light"
                color="grey"
                className="flex flex-row items-center space-x-1"
              >
                <Spin size="small" />
                <span>Running...</span>
              </Badge>
            )}
            <Button
              onClick={() => {
                setIsExpanded(!isExpanded);
              }}
              type="neutral"
              icon={
                isExpanded === true ? (
                  <ChevronUp className="size-3.5" />
                ) : (
                  <ChevronDown className="size-3.5" />
                )
              }
            />
          </div>
        }
        subHeader={
          isExpanded === true && result !== undefined ? (
            <InputSearch
              className="my-2.5"
              value={searchValue}
              placeholder="Search..."
              onChange={(event) => {
                setSearchValue(event.target.value);
              }}
            />
          ) : null
        }
      >
        {isExpanded === true && result !== undefined ? (
          <JsonViewer data={result} />
        ) : null}
        {isExpanded === true && result === undefined ? (
          <Form
            id={form.id}
            data={form.state.data}
            jsonSchema={jsonSchema}
            uiSchema={uiSchema}
            widgets={widgets}
            context={formContext}
            isDisabled={true}
            onChange={form.state.setData}
          />
        ) : null}
      </ScreenBody>
    </div>
  );
};
