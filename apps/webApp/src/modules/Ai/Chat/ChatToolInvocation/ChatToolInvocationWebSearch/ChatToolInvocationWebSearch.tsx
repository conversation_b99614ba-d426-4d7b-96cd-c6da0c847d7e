import Globe01 from "@untitled-ui/icons-react/build/esm/Globe01";
import { Button } from "cargo-components/Button";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Title } from "cargo-components/Title";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const SOURCES_LENGTH = 4;

type Props = {
  args: { prompt: string };
  result?: {
    sources: {
      id: string;
      url: string;
      title?: string;
    }[];
  };
};

export const ChatToolInvocationWebSearch: React.FC<Props> = (props) => {
  const { args, result } = props;

  const [showAllSources, setShowAllSources] = React.useState<boolean>(false);

  const handleViewMore = () => {
    setShowAllSources(true);
  };

  const sources = React.useMemo(() => {
    return result !== undefined ? result.sources : [];
  }, [result]);

  const displayedSources = React.useMemo(() => {
    return showAllSources ? sources : sources.slice(0, SOURCES_LENGTH);
  }, [showAllSources, sources]);

  const additionalSourcesCount =
    sources.length > SOURCES_LENGTH ? sources.length - SOURCES_LENGTH : 0;

  return (
    <div className="rounded-md border border-neutral-300/50 dark:border-neutral-700/50">
      <ScreenBody
        title={
          <div className="flex flex-row items-center space-x-2">
            <Globe01 className="size-4" />
            <Title level={3} className="truncate">
              {args.prompt}
            </Title>
          </div>
        }
        className="p-2"
      >
        <div className="flex flex-wrap gap-2">
          {result === undefined
            ? [...Array(SOURCES_LENGTH)].map((_, index) => (
                <div key={index} className="w-[calc(25%-0.5rem)]">
                  <div className="h-20 w-full animate-pulse rounded-md bg-black/20 dark:bg-white/20"></div>
                </div>
              ))
            : null}
          {displayedSources.map((source, index) => (
            <div className="w-[calc(25%-0.5rem)]" key={index}>
              <Link href={source.url} passHref target="_blank">
                <div className="h-full flex-1 rounded-md border border-neutral-300/50 hover:bg-neutral-100 dark:border-neutral-700/50 dark:hover:bg-neutral-800">
                  <div className="flex h-full flex-col p-2">
                    <p className="line-clamp-2 min-h-0 flex-1 text-xs">
                      {source.title !== "" ? source.title : source.url}
                    </p>
                    <div className="mt-2 flex items-center space-x-1.5">
                      <span className="relative flex h-4 w-4 shrink-0 overflow-hidden rounded-sm">
                        <Image
                          className="aspect-square h-full w-full"
                          alt={source.url}
                          src={`https://www.google.com/s2/favicons?domain=${
                            new URL(source.url).hostname
                          }`}
                          width={16}
                          height={16}
                        />
                      </span>
                      <div className="flex min-w-0 flex-1 items-center justify-between">
                        <div className="mr-1 truncate text-xs underline">
                          {source.url}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
          {additionalSourcesCount > 0 && showAllSources === false ? (
            <div className="flex w-full justify-center">
              <Button
                type="link"
                text={`View ${additionalSourcesCount} more`}
                onClick={handleViewMore}
              />
            </div>
          ) : null}
        </div>
      </ScreenBody>
    </div>
  );
};
