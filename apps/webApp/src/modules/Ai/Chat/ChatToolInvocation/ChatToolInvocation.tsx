import type { ToolInvocation } from "ai";
import type { Ai } from "cargo-api";
import React from "react";
import { snakeCase } from "snake-case";

import { ChatToolInvocationResource } from "./ChatToolInvocationResource/ChatToolInvocationResource";
import { ChatToolInvocationTool } from "./ChatToolInvocationTool/ChatToolInvocationTool";
import { ChatToolInvocationWebSearch } from "./ChatToolInvocationWebSearch/ChatToolInvocationWebSearch";

type Props = {
  release: Ai.Release;
  toolInvocation: ToolInvocation;
};

export const ChatToolInvocation: React.FC<Props> = (props) => {
  const { release, toolInvocation } = props;
  const { toolName, toolCallId, state } = toolInvocation;

  const tool = React.useMemo(() => {
    return release.tools.find((tool) => {
      const slug = tool.slug !== undefined ? tool.slug : snakeCase(tool.name);

      return slug === toolName;
    });
  }, [release.tools, toolName]);

  const resource = React.useMemo(() => {
    return release.resources.find((resource) => {
      const slug =
        resource.slug !== undefined ? resource.slug : snakeCase(resource.name);

      return slug === toolName;
    });
  }, [release.resources, toolName]);

  if (toolName === "_webSearch") {
    return (
      <div key={toolCallId}>
        <ChatToolInvocationWebSearch
          state={state}
          args={toolInvocation.args}
          result={state === "result" ? toolInvocation.result : undefined}
        />
      </div>
    );
  }

  if (tool !== undefined) {
    return (
      <div key={toolCallId}>
        <ChatToolInvocationTool
          tool={tool}
          state={state}
          args={toolInvocation.args}
          result={state === "result" ? toolInvocation.result : undefined}
        />
      </div>
    );
  }

  if (resource !== undefined) {
    return (
      <div key={toolCallId}>
        <ChatToolInvocationResource
          resource={resource}
          state={state}
          args={toolInvocation.args}
          result={state === "result" ? toolInvocation.result : undefined}
        />
      </div>
    );
  }

  return null;
};
