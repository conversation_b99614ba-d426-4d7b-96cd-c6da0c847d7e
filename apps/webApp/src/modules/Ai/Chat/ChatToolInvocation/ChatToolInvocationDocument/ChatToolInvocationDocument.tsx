import File02 from "@untitled-ui/icons-react/build/esm/File02";
import MessageCircle01 from "@untitled-ui/icons-react/build/esm/MessageCircle01";
import Pencil01 from "@untitled-ui/icons-react/build/esm/Pencil01";
import { Spin } from "cargo-components/Spin";
import React from "react";

import { useArtifact } from "../../hooks/useArtifact";

const getActionText = (
  type: "create" | "update" | "request-suggestions",
  tense: "present" | "past",
) => {
  switch (type) {
    case "create":
      return tense === "present" ? "Creating" : "Created";
    case "update":
      return tense === "present" ? "Updating" : "Updated";
    case "request-suggestions":
      return tense === "present"
        ? "Adding suggestions"
        : "Added suggestions to";
    default:
      return null;
  }
};

type Props = {
  type: "create" | "update" | "request-suggestions";
  args: { title: string };
  result?: { id: string; title: string; kind: string };
};

export const ChatToolInvocationDocument: React.FC<Props> = (props) => {
  const { type, args, result } = props;

  const { setArtifact } = useArtifact();

  if (result === undefined) {
    return (
      <button
        type="button"
        className="flex w-fit flex-row items-start justify-between gap-3 rounded-xl border border-neutral-300/50 px-3 py-2 dark:border-neutral-700/50"
        onClick={(event) => {
          const rect = event.currentTarget.getBoundingClientRect();

          const boundingBox = {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height,
          };

          setArtifact((currentArtifact) => ({
            ...currentArtifact,
            isVisible: true,
            boundingBox,
          }));
        }}
      >
        <div className="flex flex-row items-center space-x-2">
          <div>
            {type === "create" ? (
              <File02 className="size-4" />
            ) : type === "update" ? (
              <Pencil01 className="size-4" />
            ) : type === "request-suggestions" ? (
              <MessageCircle01 className="size-4" />
            ) : null}
          </div>

          <div className="min-w-0 flex-1 text-left">
            {`${getActionText(type, "present")} ${args.title ? `"${args.title}"` : ""}`}
          </div>
          <div>{<Spin size="small" />}</div>
        </div>{" "}
      </button>
    );
  }

  return (
    <button
      type="button"
      className="flex w-fit cursor-pointer flex-row items-start gap-3 rounded-xl border border-neutral-300/50 px-3 py-2 dark:border-neutral-700/50"
      onClick={(event) => {
        const rect = event.currentTarget.getBoundingClientRect();

        const boundingBox = {
          top: rect.top,
          left: rect.left,
          width: rect.width,
          height: rect.height,
        };

        setArtifact({
          documentId: result.id,
          kind: result.kind,
          content: "",
          title: result.title,
          isVisible: true,
          status: "idle",
          boundingBox,
        });
      }}
    >
      <div>
        {type === "create" ? (
          <File02 className="size-4" />
        ) : type === "update" ? (
          <Pencil01 className="size-4" />
        ) : type === "request-suggestions" ? (
          <MessageCircle01 className="size-4" />
        ) : null}
      </div>
      <div className="text-left">
        {`${getActionText(type, "past")} "${result.title}"`}
      </div>
    </button>
  );
};
