import ArrowDown from "@untitled-ui/icons-react/build/esm/ArrowDown";
import ArrowUp from "@untitled-ui/icons-react/build/esm/ArrowUp";
import CheckCircle from "@untitled-ui/icons-react/build/esm/CheckCircle";
import { type Ai, useStore } from "cargo-api";
import { Badge } from "cargo-components/Badge";
import { Button } from "cargo-components/Button";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Spin } from "cargo-components/Spin";
import { type TableRecord } from "cargo-components/Table/types";
import { Title } from "cargo-components/Title";
import { getKeys, stringifyJson } from "cargo-utils";
import { Form, useForm } from "components/Form";
import { LazyTable } from "components/LazyTable";
import { ModelIcon } from "modules/Storage/ModelIcon/ModelIcon";
import React from "react";

type Props = {
  state: "partial-call" | "call" | "result";
  resource: Ai.Resource;
  args: Record<string, unknown>;
  result?: {
    records?: Record<string, unknown>[];
  };
};

export const ChatToolInvocationResource: React.FC<Props> = (props) => {
  const { resource, args, result, state } = props;

  const models = useStore((state) => state.models);

  const [isExpanded, setIsExpanded] = React.useState<boolean>(false);

  const model = React.useMemo(() => {
    return models.find((model) => model.uuid === resource.modelUuid);
  }, [models, resource.modelUuid]);

  const records = React.useMemo(() => {
    return result !== undefined && result.records !== undefined
      ? result.records
      : [];
  }, [result]);

  const tableColumns = React.useMemo(() => {
    const [firstRecord] = records;

    if (firstRecord === undefined) {
      return [];
    }

    return getKeys(firstRecord).map((key) => {
      return {
        label: (
          <div className="flex h-full w-full flex-row items-center justify-start">
            <div className="min-w-0 flex-1">
              <div className="truncate text-left font-medium">{key}</div>
            </div>
          </div>
        ),
        slug: key,
        size: 200,
      };
    });
  }, [records]);

  const form = useForm<Record<string, unknown>>({
    initialData: args,
  });

  const tableRecords = React.useMemo(() => {
    if (records.length === 0) {
      return [];
    }

    return records.map<TableRecord>((record) => {
      return Object.entries(record).reduce<TableRecord>(
        (currentReshapedRecord, [key, value]) => {
          currentReshapedRecord[key] = <>{stringifyJson(value)}</>;

          return currentReshapedRecord;
        },
        {},
      );
    });
  }, [records]);

  return (
    <div className="rounded-md border border-neutral-300/50 dark:border-neutral-700/50">
      <ScreenBody
        title={
          <div className="flex flex-row items-center space-x-2">
            {model !== undefined ? <ModelIcon model={model} /> : null}
            <Title level={3}>{resource.name}</Title>
          </div>
        }
        header={
          <div className="flex flex-row space-x-2">
            {state === "result" ? (
              <Badge
                variant="light"
                color="green"
                className="flex flex-row items-center space-x-1"
              >
                <CheckCircle className="size-4" />
                <span>Success</span>
              </Badge>
            ) : (
              <Badge
                variant="light"
                color="grey"
                className="flex flex-row items-center space-x-1"
              >
                <Spin size="small" />
                <span>Running...</span>
              </Badge>
            )}
            <Button
              onClick={() => {
                setIsExpanded(!isExpanded);
              }}
              type="neutral"
              icon={
                isExpanded === true ? (
                  <ArrowUp className="size-3.5" />
                ) : (
                  <ArrowDown className="size-3.5" />
                )
              }
            />
          </div>
        }
        className="max-h-[calc(30dvh)]"
      >
        {isExpanded === true && state === "result" ? (
          <LazyTable
            columns={tableColumns}
            records={tableRecords}
            className="h-full w-full"
          />
        ) : null}

        {isExpanded === true && state === "call" ? (
          <Form
            id={form.id}
            data={form.state.data}
            jsonSchema={{
              type: "object",
              properties: {
                question: {
                  title: "Question",
                  type: "string",
                },
              },
              required: ["question"],
            }}
            uiSchema={{}}
            isDisabled={true}
            onChange={form.state.setData}
          />
        ) : null}
      </ScreenBody>
    </div>
  );
};
