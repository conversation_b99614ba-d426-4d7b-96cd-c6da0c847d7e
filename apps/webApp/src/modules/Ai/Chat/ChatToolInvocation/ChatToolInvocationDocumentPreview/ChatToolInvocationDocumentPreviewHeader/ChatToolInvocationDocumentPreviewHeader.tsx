import File02 from "@untitled-ui/icons-react/build/esm/File02";
import Image01 from "@untitled-ui/icons-react/build/esm/Image01";
import type { Ai } from "cargo-api";
import { Spin } from "cargo-components/Spin";
import React from "react";

type Props = {
  title: string;
  kind: Ai.DocumentKind;
  isStreaming: boolean;
};

const PureChatToolInvocationDocumentPreviewHeader: React.FC<Props> = (
  props,
) => {
  const { title, kind, isStreaming } = props;

  return (
    <div className="flex h-12 flex-row items-center justify-between gap-2 rounded-t-md border border-b border-neutral-300/50 p-4 dark:border-neutral-700/50">
      <div className="flex flex-row items-center gap-3">
        <div>
          {isStreaming === true ? (
            <div>
              <Spin size="small" />
            </div>
          ) : kind === "image" ? (
            <Image01 className="size-4" />
          ) : (
            <File02 className="size-4" />
          )}
        </div>
        <div className="translate-y-0 font-medium">{title}</div>
      </div>
      <div className="w-8" />
    </div>
  );
};

export const ChatToolInvocationDocumentPreviewHeader = React.memo(
  PureChatToolInvocationDocumentPreviewHeader,
  (prevProps, nextProps) => {
    if (prevProps.title !== nextProps.title) return false;
    if (prevProps.isStreaming !== nextProps.isStreaming) return false;

    return true;
  },
);
