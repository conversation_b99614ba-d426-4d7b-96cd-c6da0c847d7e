import Expand01 from "@untitled-ui/icons-react/build/esm/Expand01";
import { equals } from "cargo-utils";
import React from "react";

import type { Artifact } from "../../../ChatArtifact/definition/definition";

type Props = {
  hitboxRef: React.RefObject<HTMLDivElement>;
  result: any;
  setArtifact: (
    updaterFn: Artifact | ((currentArtifact: Artifact) => Artifact),
  ) => void;
};

export const PureChatToolInvocationDocumentPreviewHitbox: React.FC<Props> = (
  props,
) => {
  const { hitboxRef, result, setArtifact } = props;

  const handleClick = React.useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      const boundingBox = event.currentTarget.getBoundingClientRect();

      setArtifact((artifact) =>
        artifact.status === "streaming"
          ? { ...artifact, isVisible: true }
          : {
              ...artifact,
              title: result.title,
              documentId: result.id,
              kind: result.kind,
              isVisible: true,
              boundingBox: {
                left: boundingBox.x,
                top: boundingBox.y,
                width: boundingBox.width,
                height: boundingBox.height,
              },
            },
      );
    },
    [setArtifact, result],
  );

  return (
    <div
      className="z-10 absolute left-0 top-0 size-full rounded-md"
      ref={hitboxRef}
      onClick={handleClick}
      role="presentation"
      aria-hidden="true"
    >
      <div className="flex w-full items-center justify-end p-4">
        <div className="absolute right-2 top-2 rounded-md p-2 hover:bg-neutral-100 hover:dark:bg-neutral-800">
          <Expand01 className="size-4" />
        </div>
      </div>
    </div>
  );
};

export const ChatToolInvocationDocumentPreviewHitbox = React.memo(
  PureChatToolInvocationDocumentPreviewHitbox,
  (prevProps, nextProps) => {
    if (equals(prevProps.result, nextProps.result) === false) return false;
    return true;
  },
);
