import type { Ai } from "cargo-api";
import classNames from "classnames";

import { Editor as CodeEditor } from "../../../ChatArtifact/definition/code/Editor";
import { Editor as ImageEditor } from "../../../ChatArtifact/definition/image/Editor";
import { Editor as SpreadsheetEditor } from "../../../ChatArtifact/definition/sheet/Editor";
import { Editor as TextEditor } from "../../../ChatArtifact/definition/text/Editor";
import { useArtifact } from "../../../hooks/useArtifact";

type Props = {
  document: Ai.Document;
};

export const ChatToolInvocationDocumentPreviewContent: React.FC<Props> = (
  props,
) => {
  const { document } = props;

  const { artifact } = useArtifact();

  const containerClassName = classNames(
    "h-[257px] overflow-y-scroll rounded-b-2xl border border-t-0 border-neutral-300/50 dark:border-neutral-700/50",
    {
      "px-14 py-16": document.kind === "text",
      "p-0": document.kind === "code",
    },
  );

  const commonProps = {
    content: document.content,
    isCurrentVersion: true,
    currentVersionIndex: 0,
    status: artifact.status,
    saveContent: () => {},
    suggestions: [],
  };

  return (
    <div className={containerClassName}>
      {document.kind === "text" ? (
        <TextEditor {...commonProps} onSaveContent={() => {}} />
      ) : null}
      {document.kind === "code" ? (
        <div className="relative flex w-full flex-1">
          <div className="absolute inset-0">
            <CodeEditor {...commonProps} onSaveContent={() => {}} />
          </div>
        </div>
      ) : null}
      {document.kind === "sheet" ? (
        <div className="relative flex size-full flex-1 p-4">
          <div className="absolute inset-0">
            <SpreadsheetEditor {...commonProps} />
          </div>
        </div>
      ) : null}
      {document.kind === "image" ? (
        <ImageEditor
          title={document.title !== undefined ? document.title : ""}
          content={document.content}
          isCurrentVersion={true}
          currentVersionIndex={0}
          status={artifact.status}
          isInline={true}
        />
      ) : null}
    </div>
  );
};
