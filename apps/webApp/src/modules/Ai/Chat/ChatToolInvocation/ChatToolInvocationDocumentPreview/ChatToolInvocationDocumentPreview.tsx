"use client";

import Expand01 from "@untitled-ui/icons-react/build/esm/Expand01";
import { Ai } from "cargo-api";
import { ApiContext } from "contexts/ApiContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import React from "react";
import { useQuery } from "react-query";

import { useArtifact } from "../../hooks/useArtifact";
import { ChatToolInvocationDocument } from "../ChatToolInvocationDocument/ChatToolInvocationDocument";
import { ChatToolInvocationDocumentPreviewContent } from "./ChatToolInvocationDocumentPreviewContent/ChatToolInvocationDocumentPreviewContent";
import { ChatToolInvocationDocumentPreviewHeader } from "./ChatToolInvocationDocumentPreviewHeader/ChatToolInvocationDocumentPreviewHeader";
import { ChatToolInvocationDocumentPreviewHitbox } from "./ChatToolInvocationDocumentPreviewHitbox/ChatToolInvocationDocumentPreviewHitbox";

type Props = {
  result?: { id: string; title: string; kind: string };
  args: { title: string; kind: string };
};

export const ChatToolInvocationDocumentPreview: React.FC<Props> = (props) => {
  const { result, args } = props;

  const { artifact, setArtifact } = useArtifact();

  const { workspaceUuid } = React.useContext(WorkspaceContext);
  const { api } = React.useContext(ApiContext);

  const getDocumentState = useQuery(
    Ai.documentKeys.get(workspaceUuid, result?.id as string),
    () => api.ai.document.get(result?.id as string),
    { enabled: result !== undefined },
  );

  const document = React.useMemo(() => {
    if (getDocumentState.isSuccess === true) {
      return getDocumentState.data.document;
    }

    if (artifact.status === "streaming") {
      return {
        workspaceUuid,
        title: artifact.title,
        kind: artifact.kind as Ai.DocumentKind,
        content: artifact.content,
        uuid: artifact.documentId,
        createdAt: new Date(),
        deletedAt: undefined,
        userUuid: "noop",
      };
    }

    return undefined;
  }, [
    artifact.content,
    artifact.documentId,
    artifact.kind,
    artifact.status,
    artifact.title,
    getDocumentState.data,
    getDocumentState.isSuccess,
    workspaceUuid,
  ]);

  const hitboxRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const boundingBox = hitboxRef.current?.getBoundingClientRect();

    if (artifact.documentId && boundingBox) {
      setArtifact((artifact) => ({
        ...artifact,
        boundingBox: {
          left: boundingBox.x,
          top: boundingBox.y,
          width: boundingBox.width,
          height: boundingBox.height,
        },
      }));
    }
  }, [artifact.documentId, setArtifact]);

  if (artifact.isVisible === true) {
    return (
      <ChatToolInvocationDocument type="create" args={args} result={result} />
    );
  }

  if (getDocumentState.isLoading === true) {
    return (
      <LoadingSkeleton
        artifactKind={
          result !== undefined && result.kind !== undefined
            ? result.kind
            : args.kind
        }
      />
    );
  }

  if (document === undefined) {
    return <LoadingSkeleton artifactKind={artifact.kind} />;
  }

  return (
    <div className="relative w-full cursor-pointer">
      <ChatToolInvocationDocumentPreviewHeader
        title={document.title !== undefined ? document.title : ""}
        kind={document.kind}
        isStreaming={artifact.status === "streaming"}
      />
      <ChatToolInvocationDocumentPreviewContent document={document} />
      <ChatToolInvocationDocumentPreviewHitbox
        hitboxRef={hitboxRef}
        result={result}
        setArtifact={setArtifact}
      />
    </div>
  );
};

const LoadingSkeleton = ({ artifactKind }: { artifactKind: string }) => (
  <div className="w-full">
    <div className="flex h-[57px] flex-row items-center justify-between gap-2 rounded-t-2xl border border-b-0 border-neutral-300/50 p-4 dark:border-neutral-700/50">
      <div className="flex flex-row items-center gap-3">
        <div>
          <div className="size-4 animate-pulse rounded-md bg-black/20 dark:bg-white/20" />
        </div>
        <div className="h-4 w-24 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
      </div>
      <div>
        <Expand01 className="size-4" />
      </div>
    </div>
    {artifactKind === "image" ? (
      <div className="overflow-y-scroll rounded-b-2xl border border-t-0 border-neutral-300/50 dark:border-neutral-700/50">
        <div className="h-[257px] w-full animate-pulse bg-black/20 dark:bg-white/20" />
      </div>
    ) : (
      <div className="overflow-y-scroll rounded-b-2xl border border-t-0 border-neutral-300/50 p-8 pt-4 dark:border-neutral-700/50">
        <div className="flex w-full flex-col gap-4">
          <div className="h-4 w-48 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
          <div className="h-4 w-3/4 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
          <div className="h-4 w-1/2 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
          <div className="h-4 w-64 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
          <div className="h-4 w-40 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
          <div className="h-4 w-36 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
          <div className="h-4 w-64 animate-pulse rounded-lg bg-black/20 dark:bg-white/20" />
        </div>
      </div>
    )}
  </div>
);
