import { determineIfIsFetcherError, type Orchestration } from "cargo-api";
import { Alert } from "cargo-components/Alert";
import { Button } from "cargo-components/Button";
import { Input } from "cargo-components/Input";
import { Modal } from "cargo-components/Modal";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import React from "react";
import { useMutation } from "react-query";

const CONFIRMATION_TEXT = "Cancel";

type Props = {
  workflowUuid: string;
  filter: Orchestration.BatchDataRunsFilter;
  isOpen: boolean;
  onSuccess: () => void;
  onCancel: () => void;
};

export const RunsCancelModal: React.FC<Props> = (props) => {
  const { workflowUuid, filter, isOpen, onSuccess, onCancel } = props;

  const { api } = React.useContext(ApiContext);
  const { addNotification } = React.useContext(NotificationContext);

  const [confirmationText, setConfirmationText] = React.useState<
    string | undefined
  >(undefined);

  const inputRef = React.useRef<HTMLInputElement | null>(null);

  const runsCancelState = useMutation(api.orchestration.run.cancel, {
    onSuccess: () => {
      onSuccess();
      addNotification({
        title: `Cancelling runs...`,
        type: "info",
      });
    },
  });

  const recordsCancelState = useMutation(api.orchestration.record.cancel, {
    onSuccess: () => {
      onSuccess();
      addNotification({
        title: `Cancelling runs...`,
        type: "info",
      });
    },
  });

  const formId = React.useId();

  const isSubmitDisabled = confirmationText !== CONFIRMATION_TEXT;

  return (
    <Modal
      title={`Confirm you want to cancel these runs?`}
      initialFocus={inputRef}
      hideCancel={true}
      hideOk={true}
      footer={
        <Button
          htmlType="submit"
          form={formId}
          type="danger"
          text="Confirm"
          className="w-full"
          isLoading={
            runsCancelState.isLoading === true ||
            recordsCancelState.isLoading === true
          }
          isDisabled={isSubmitDisabled === true}
        />
      }
      className="w-[400px]"
      isOpen={isOpen}
      onUnmount={() => {
        setConfirmationText(undefined);
      }}
      onCancel={() => {
        onCancel();
      }}
    >
      {determineIfIsFetcherError(runsCancelState.error) === true ? (
        <Alert type="error">{runsCancelState.error.message}</Alert>
      ) : null}

      {determineIfIsFetcherError(recordsCancelState.error) === true ? (
        <Alert type="error">{recordsCancelState.error.message}</Alert>
      ) : null}

      <form
        id={formId}
        className="p-2"
        onSubmit={(event) => {
          event.preventDefault();

          if (isSubmitDisabled === true) {
            return;
          }

          if (filter.kind === "records") {
            recordsCancelState.mutate({
              workflowUuid,
              ...filter,
            });

            return;
          }

          runsCancelState.mutate({
            workflowUuid,
            ...filter,
          });
        }}
      >
        <Input
          forwardedRef={inputRef}
          label="Text"
          description={`To confirm the cancellation of the runs, please type "${CONFIRMATION_TEXT}"`}
          isRequired={true}
          type="danger"
          htmlType="text"
          value={confirmationText}
          onChange={setConfirmationText}
        />
      </form>
    </Modal>
  );
};
