import type { Variants } from "framer-motion";
import moment from "moment-timezone";

import type { UsagePeriod } from "./types";

export function getItemColor(
  itemKeys: string[],
  itemUuid: string,
  max: number,
  colors: string[],
) {
  const colorMapping = itemKeys.reduce(
    (acc, key, index) => {
      acc[key] = index < max ? colors[index] : colors[max];
      return acc;
    },
    {} as Record<string, string | undefined>,
  );

  return colorMapping[itemUuid] || "#A3A3A3";
}

export function getUsagePeriodDates(usagePeriod?: UsagePeriod) {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  if (usagePeriod !== undefined) {
    return {
      from: moment.tz(usagePeriod.from, timezone).startOf("day").toDate(),
      to: moment.tz(usagePeriod.to, timezone).endOf("day").toDate(),
    };
  }

  const now = moment.tz(timezone);

  return {
    from: now.clone().startOf("month").startOf("day").toDate(),
    to: now.clone().endOf("month").endOf("day").toDate(),
  };
}

export const percentageFormatter = new Intl.NumberFormat("en-US", {
  style: "percent",
  minimumFractionDigits: 1,
  maximumFractionDigits: 2,
});

export const numberFormatter = new Intl.NumberFormat("en-US", {
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

export const listAnimationVariants: Variants = {
  initial: { opacity: 0 },
  enter: (index: number) => ({
    opacity: 1,
    transition: {
      delay: index * 0.1,
      duration: 0.3,
      type: "keyframes",
    },
  }),
  leave: {
    opacity: 0,
    transition: {
      duration: 0.2,
      type: "keyframes",
    },
  },
};
