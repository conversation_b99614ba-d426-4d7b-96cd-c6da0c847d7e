import { useStore } from "cargo-api";
import type { BillingTypes } from "cargo-domain-types";
import { useCountAnimation } from "cargo-react-utils";
import { motion } from "framer-motion";
import { ModelIcon } from "modules/Storage/ModelIcon/ModelIcon";
import moment from "moment";
import React from "react";

import {
  listAnimationVariants,
  numberFormatter,
  percentageFormatter,
} from "../../utils";

type Props = {
  items: BillingTypes.UsageMetric["items"];
  from: Date;
  to: Date;
  totalRecordsCount: number;
};

export const SettingsUsageStorageTable: React.FC<Props> = (props) => {
  const { items, totalRecordsCount, from, to } = props;

  const models = useStore((state) => state.models);

  const formattedTitle = React.useMemo(() => {
    if (from !== undefined && to !== undefined) {
      return `${moment(from).format("MMM YYYY")} - ${moment(to).format("MMM YYYY")}`;
    }

    return moment(new Date()).format("MMM YYYY");
  }, [from, to]);

  const animatedTotalRecordsCount = useCountAnimation(totalRecordsCount);

  const animationKey = React.useMemo(
    () => `${from.toString()}-${to.toString()}`,
    [from, to],
  );

  return (
    <table className="min-w-full table-fixed text-sm font-normal leading-5 text-neutral-500 dark:text-neutral-400">
      <thead className="sticky top-0 z-layer-1 bg-white shadow-[0_1px_0px_0px_rgba(0,0,0,0.12)] dark:bg-neutral-900 dark:shadow-[0_1px_0px_0px_rgba(255,255,255,0.1)]">
        <tr className="py-2.5 text-sm font-medium leading-4 text-neutral-800 dark:text-neutral-100">
          <motion.th
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="py-2 text-left font-medium"
          >
            {formattedTitle}
          </motion.th>
          <th className="py-2 text-right font-medium">
            {numberFormatter.format(animatedTotalRecordsCount)} Records
          </th>
          <th className="w-32 py-2 pr-4 text-right font-medium">100%</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colSpan={3} className="h-3"></td>
        </tr>
        {items.map((item, index) => {
          const percentage = item.creditsUsedCount / totalRecordsCount;
          const model = models.find((model) => model.uuid === item.uuid);

          if (model === undefined) {
            return null;
          }

          return (
            <motion.tr
              key={`${item.uuid}-${animationKey}`}
              custom={index}
              animate="enter"
              initial="initial"
              exit="leave"
              variants={listAnimationVariants}
              className="w-full transition-all duration-300 ease-in-out"
            >
              <td className="rounded-l-md py-2 pl-2">
                <div className="flex items-center">
                  <ModelIcon model={model} size="small" className="mr-2" />
                  <span className="text-sm font-medium leading-5 text-neutral-800 dark:text-neutral-100">
                    {model.name}
                  </span>
                </div>
              </td>

              <td className="py-2 text-right">
                {numberFormatter.format(item.creditsUsedCount)}
              </td>

              <td className="rounded-r-md py-2 pr-3 text-right">
                {percentageFormatter.format(percentage)}
              </td>
            </motion.tr>
          );
        })}
      </tbody>
    </table>
  );
};
