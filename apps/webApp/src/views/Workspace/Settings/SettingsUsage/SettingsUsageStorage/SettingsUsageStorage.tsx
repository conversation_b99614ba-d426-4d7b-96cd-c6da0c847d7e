import { Billing } from "cargo-api";
import type { ChartOptions } from "cargo-components/Chart";
import { Chart } from "cargo-components/Chart";
import { Spin } from "cargo-components/Spin";
import type { BillingTypes } from "cargo-domain-types";
import { LazyDatePicker } from "components/LazyDatePicker";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import moment from "moment";
import { useTheme } from "next-themes";
import React from "react";
import { useQuery } from "react-query";
import resolveConfig from "tailwindcss/resolveConfig";

import tailwindConfig from "../../../../../../tailwind.config";
import type { ChartData, UsagePeriod } from "../types";
import { getUsagePeriodDates, numberFormatter } from "../utils";
import { SettingsUsageStorageTable } from "./SettingsUsageStorageTable/SettingsUsageStorageTable";

const computedTailwindConfig = resolveConfig(tailwindConfig);

const chartColorMap: Record<
  string,
  Record<"ticks" | "grid" | "hoverBackgroundColor", string>
> = {
  light: {
    ticks: computedTailwindConfig.theme.colors.neutral[400],
    grid: `${computedTailwindConfig.theme.colors.neutral[400]}50`,
    hoverBackgroundColor: `${computedTailwindConfig.theme.colors.neutral[200]}80`,
  },
  dark: {
    ticks: computedTailwindConfig.theme.colors.neutral[600],
    grid: computedTailwindConfig.theme.colors.neutral[800],
    hoverBackgroundColor: computedTailwindConfig.theme.colors.neutral[800],
  },
};

export const SettingsUsageStorage: React.FC = () => {
  const { resolvedTheme: theme = "light" } = useTheme();

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);
  const { workspaceUuid } = React.useContext(WorkspaceContext);

  const defaultPeriod = React.useMemo<UsagePeriod>(() => {
    return getUsagePeriodDates();
  }, []);

  const [usagePeriod, setUsagePeriod] = React.useState<UsagePeriod | undefined>(
    undefined,
  );

  const { from, to } = React.useMemo(() => {
    if (usagePeriod !== undefined) {
      return getUsagePeriodDates(usagePeriod);
    }
    return defaultPeriod;
  }, [usagePeriod, defaultPeriod]);

  const chartColor = React.useMemo(() => {
    return chartColorMap[theme];
  }, [theme]);

  const usageMetricsPayload =
    React.useMemo<Billing.Api.UsageMetricsPayload>(() => {
      return {
        from: moment(from).format("YYYY-MM-DD"),
        to: moment(to).format("YYYY-MM-DD"),
        workspaceUuid,
        unit: "storage.records",
        groupBy: "model_uuid",
      };
    }, [from, to, workspaceUuid]);

  const getUsageMetricsState = useQuery(
    Billing.usagesKeys.getMetrics(workspaceUuid, usageMetricsPayload),
    () => api.billing.usages.getMetrics(usageMetricsPayload),
    {
      onError: () => {
        addNotification({
          title: "An unknown error has occurred",
          type: "error",
        });
      },
      keepPreviousData: false,
    },
  );

  const metrics = React.useMemo(() => {
    if (getUsageMetricsState.isSuccess === false) {
      return [];
    }

    const metrics = getUsageMetricsState.data.metrics;
    return metrics;
  }, [getUsageMetricsState.data?.metrics, getUsageMetricsState.isSuccess]);

  const chartData = React.useMemo(() => {
    const totalConsumptionByModel: Record<string, number> = {};

    const start = moment.utc(from);
    const end = moment.utc(to);

    const totalDays = end.diff(start, "days") + 1;

    const allDaysInPeriod = Array.from({ length: totalDays }, (_, index) => {
      const date = start.clone().add(index, "days");
      return {
        day: date.format("YYYY-MM-DD"),
        displayDay: date.format("DD"),
      };
    });

    const chartData: ChartData[] = [...allDaysInPeriod];

    for (const metric of metrics) {
      const day = moment.utc(metric.date).format("YYYY-MM-DD");

      const existingDayIndex = chartData.findIndex(
        (dataItem) => dataItem.day === day,
      );

      const modelsData: Record<string, number> = {};

      for (const model of metric.items) {
        totalConsumptionByModel[model.uuid] =
          (totalConsumptionByModel[model.uuid] || 0) + model.creditsUsedCount;
        modelsData[model.uuid] = model.creditsUsedCount;
      }

      if (existingDayIndex > -1) {
        chartData[existingDayIndex] = {
          ...chartData[existingDayIndex],
          ...modelsData,
        } as ChartData;
      }
    }

    return chartData;
  }, [from, metrics, to]);

  const models = React.useMemo<BillingTypes.UsageMetric["items"]>(() => {
    const result = new Map<string, BillingTypes.UsageMetric["items"][0]>();

    for (const metric of metrics) {
      const metricDay = moment(metric.date).format("YYYY-MM-DD");
      const fromDay = moment(from).format("YYYY-MM-DD");
      const toDay = moment(to).format("YYYY-MM-DD");

      if (metricDay >= fromDay && metricDay <= toDay) {
        for (const item of metric.items) {
          if (result.has(item.uuid)) {
            result.get(item.uuid)!.creditsUsedCount += item.creditsUsedCount;
          } else {
            result.set(item.uuid, { ...item });
          }
        }
      }
    }

    const sortedModels = Array.from(result.values()).sort((a, b) => {
      const modelATotalConsumption = a.creditsUsedCount || 0;
      const modelBTotalConsumption = b.creditsUsedCount || 0;

      return modelBTotalConsumption - modelATotalConsumption;
    });

    return sortedModels;
  }, [from, to, metrics]);

  const totalRecordsCount = React.useMemo(() => {
    return models.reduce((total, model) => {
      return total + model.creditsUsedCount;
    }, 0);
  }, [models]);

  const selectUsagePeriod = React.useCallback((dates: Date[] | undefined) => {
    if (dates === undefined) {
      setUsagePeriod(undefined);
      return;
    }

    const [from, to] = dates;

    if (from === undefined || to === undefined) {
      return;
    }

    setUsagePeriod({
      from,
      to,
    });
  }, []);

  const chartConfig = React.useMemo(() => {
    const sortedChartData = chartData.sort(
      (a, b) => Number(a.day) - Number(b.day),
    );

    const datasets = [
      {
        label: "Records",
        backgroundColor: "#22D3EE",
        data: sortedChartData.map((data) =>
          models.reduce(
            (sum, model) => sum + (Number(data[model.uuid]) || 0),
            0,
          ),
        ),
      },
    ];

    return {
      labels: chartData.map((data) => data.displayDay),
      datasets,
    };
  }, [chartData, models]);

  const chartOptions = React.useMemo<ChartOptions>(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      hoverBackgroundColor: "transparent",
      showTooltips: false,
      scales: {
        x: {
          stacked: true,
          grid: { display: false },
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
          },
        },
        y: {
          stacked: true,
          beginAtZero: true,
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
            callback: (value) => {
              return value.toLocaleString().replace(/,/g, " ");
            },
          },
          grid: {
            color: chartColor !== undefined ? chartColor.grid : undefined,
          },
          border: {
            display: false,
          },
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: false,
        },
      },
    };
  }, [chartColor]);

  return (
    <div className="flex size-full flex-col items-stretch justify-start px-7 pt-7">
      <div className="mb-4 flex shrink-0 flex-row items-stretch justify-between">
        <div className="flex-1">
          <div className="font-medium lining-nums tabular-nums">
            {numberFormatter.format(totalRecordsCount)} records
          </div>
          <div className="text-neutral-500 dark:text-neutral-400">
            stored in models
          </div>
        </div>
        <div className="w-[275px] shrink-0">
          <LazyDatePicker
            dates={[from, to]}
            onChange={selectUsagePeriod}
            isRange={true}
          />
        </div>
      </div>
      {getUsageMetricsState.isLoading === true ? (
        <Spin />
      ) : (
        <React.Fragment>
          <div className="h-50 w-full shrink-0">
            <Chart
              id="usage-storage"
              type="bar"
              options={chartOptions}
              data={chartConfig}
            />
          </div>
          <div className="mt-6 min-h-0 flex-1 overflow-y-auto pb-3">
            <SettingsUsageStorageTable
              items={models}
              from={from}
              to={to}
              totalRecordsCount={totalRecordsCount}
            />
          </div>
        </React.Fragment>
      )}
    </div>
  );
};
