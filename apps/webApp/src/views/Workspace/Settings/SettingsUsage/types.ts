import type { Orchestration, Storage } from "cargo-api";

export type ChartData = {
  day: string;
  displayDay: string;
  [key: string]: string | number;
};

export type UsagePeriod = {
  from: Date;
  to: Date;
};

export type ItemUsage =
  | {
      kind: "play";
      item: Orchestration.Play;
    }
  | {
      kind: "tool";
      item: Orchestration.Tool;
    }
  | {
      kind: "model";
      item: Storage.Model;
    };

export type OmitUnionByKind<T, K extends string> = Exclude<T, { kind: K }>;

export type WorkflowStats = {
  uuid: string;
  successCount: number;
  errorCount: number;
  total: number;
  successPercentage: number;
  errorPercentage: number;
  totalPercentageUsage: number;
};
