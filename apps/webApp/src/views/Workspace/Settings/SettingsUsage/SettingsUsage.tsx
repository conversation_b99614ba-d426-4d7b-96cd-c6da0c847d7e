import { useStore } from "cargo-api";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Tabs } from "cargo-components/Tabs";
import { TabsItem } from "cargo-components/Tabs/TabsItem";
import { ApiContext } from "contexts/ApiContext";
import { CreditsUsage } from "modules/Billing/CreditsUsage";
import React from "react";
import { useMutation } from "react-query";

import { SettingsUsageEnrichment } from "./SettingsUsageEnrichment/SettingsUsageEnrichment";
import { SettingsUsageOrchestration } from "./SettingsUsageOrchestration/SettingsUsageOrchestration";
import { SettingsUsageStorage } from "./SettingsUsageStorage/SettingsUsageStorage";

type Tab = "enrichment" | "orchestration" | "storage";

export const SettingsUsage: React.FC = () => {
  const { api } = React.useContext(ApiContext);

  const subscription = useStore((state) => state.subscription);

  const [activeTab, setActiveTab] = React.useState<Tab>("enrichment");

  const startCheckoutSessionState = useMutation(
    api.billing.subscriptions.createStripeCreditsCheckoutSession,
    {
      onSuccess: ({ checkoutSession }) => {
        window.open(checkoutSession.url, "_blank");
      },
    },
  );

  if (subscription === undefined) {
    return null;
  }

  return (
    <ScreenBody
      size="large"
      title="Usage"
      subHeader={
        <Tabs
          className="mt-1.5"
          type="underline"
          value={activeTab}
          onChange={(value) => {
            setActiveTab(value as Tab);
          }}
        >
          <TabsItem value="enrichment">Enrichment</TabsItem>
          <TabsItem value="orchestration">Orchestration</TabsItem>
          <TabsItem value="storage">Storage</TabsItem>
        </Tabs>
      }
      className="flex h-full flex-col items-start justify-start overflow-hidden"
      header={
        <CreditsUsage
          subscription={subscription}
          variant="full"
          isCheckoutLoading={
            subscription.provider === "stripe"
              ? startCheckoutSessionState.isLoading
              : false
          }
          startCheckoutSession={
            subscription.provider === "stripe"
              ? startCheckoutSessionState.mutate
              : undefined
          }
        />
      }
    >
      {activeTab === "enrichment" ? <SettingsUsageEnrichment /> : null}
      {activeTab === "orchestration" ? <SettingsUsageOrchestration /> : null}
      {activeTab === "storage" ? <SettingsUsageStorage /> : null}
    </ScreenBody>
  );
};
