import { useStore } from "cargo-api";
import { useCountAnimation } from "cargo-react-utils";
import { motion } from "framer-motion";
import { PlayIntegrations } from "modules/Orchestration/PlayIntegrations/PlayIntegrations";
import { ToolIcon } from "modules/Orchestration/ToolIcon";
import moment from "moment";
import React from "react";

import type { ItemUsage, OmitUnionByKind, WorkflowStats } from "../../types";
import {
  listAnimationVariants,
  numberFormatter,
  percentageFormatter,
} from "../../utils";

type Props = {
  workflowsStats: WorkflowStats[];
  from: Date;
  to: Date;
};

export const SettingsUsageOrchestrationTable: React.FC<Props> = (props) => {
  const { workflowsStats, from, to } = props;

  const workflows = useStore((state) => state.workflows);
  const plays = useStore((state) => state.plays);
  const tools = useStore((state) => state.tools);

  const formattedTitle = React.useMemo(() => {
    if (from !== undefined && to !== undefined) {
      return `${moment(from).format("MMM YYYY")} - ${moment(to).format("MMM YYYY")}`;
    }

    return moment(new Date()).format("MMM YYYY");
  }, [from, to]);

  const getItem = React.useCallback(
    (uuid: string): OmitUnionByKind<ItemUsage, "model"> | undefined => {
      const workflow = workflows.find((workflow) => workflow.uuid === uuid);

      if (workflow !== undefined && workflow.playUuid !== undefined) {
        const play = plays.find((play) => play.uuid === workflow.playUuid);
        if (play !== undefined) {
          return { kind: "play", item: play };
        }
      }

      if (workflow !== undefined && workflow.toolUuid !== undefined) {
        const tool = tools.find((tool) => tool.uuid === workflow.toolUuid);
        if (tool !== undefined) {
          return { kind: "tool", item: tool };
        }
      }

      return undefined;
    },
    [plays, tools, workflows],
  );

  const runStats = React.useMemo(() => {
    const successCount = workflowsStats.reduce(
      (acc, workflow) => acc + workflow.successCount,
      0,
    );
    const errorCount = workflowsStats.reduce(
      (acc, workflow) => acc + workflow.errorCount,
      0,
    );
    const totalCount = successCount + errorCount;

    return {
      successCount,
      errorCount,
      totalCount,
      successPercentage: totalCount > 0 ? successCount / totalCount : 0,
      errorPercentage: totalCount > 0 ? errorCount / totalCount : 0,
    };
  }, [workflowsStats]);

  const animatedTotalCount = useCountAnimation(runStats.totalCount);

  const animationKey = React.useMemo(
    () => `${from.toString()}-${to.toString()}`,
    [from, to],
  );

  return (
    <table className="min-w-full table-fixed text-sm font-normal leading-5 text-neutral-500 dark:text-neutral-400">
      <thead className="sticky top-0 z-layer-1 bg-white shadow-[0_1px_0px_0px_rgba(0,0,0,0.12)] dark:bg-neutral-900 dark:shadow-[0_1px_0px_0px_rgba(255,255,255,0.1)]">
        <tr className="py-2.5 text-sm font-medium leading-4 text-neutral-800 dark:text-neutral-100">
          <motion.th
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="py-2 text-left font-medium"
          >
            {formattedTitle}
          </motion.th>
          <th className="text-right font-medium">
            {numberFormatter.format(animatedTotalCount)} Steps
          </th>
          <th className="text-right font-medium">
            {percentageFormatter.format(runStats.errorPercentage)} of failures
          </th>
          <th className="text-right font-medium">100%</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colSpan={3} className="h-3"></td>
        </tr>
        {workflowsStats.map((workflow, index) => {
          const currentWorkflow = getItem(workflow.uuid);

          if (currentWorkflow === undefined) {
            return null;
          }

          return (
            <motion.tr
              custom={index}
              key={`${workflow.uuid}-${animationKey}`}
              animate="enter"
              initial="initial"
              exit="leave"
              variants={listAnimationVariants}
              className="w-full transition-all duration-300 ease-in-out"
            >
              <td className="py-2">
                <div className="flex items-center">
                  {currentWorkflow.kind === "play" && (
                    <PlayIntegrations
                      play={currentWorkflow.item}
                      size="small"
                      className="mr-2"
                    />
                  )}
                  {currentWorkflow.kind === "tool" && (
                    <ToolIcon
                      tool={currentWorkflow.item}
                      size="small"
                      className="mr-2"
                    />
                  )}
                  <span className="text-sm font-medium leading-5 text-neutral-800 dark:text-neutral-100">
                    {currentWorkflow.item.name}
                  </span>
                </div>
              </td>
              <td className="py-2 text-right">
                {numberFormatter.format(workflow.total)}
              </td>
              <td className="py-2 text-right">
                {percentageFormatter.format(workflow.errorPercentage)}
              </td>
              <td className="py-2 text-right">
                {percentageFormatter.format(workflow.totalPercentageUsage)}
              </td>
            </motion.tr>
          );
        })}
      </tbody>
    </table>
  );
};
