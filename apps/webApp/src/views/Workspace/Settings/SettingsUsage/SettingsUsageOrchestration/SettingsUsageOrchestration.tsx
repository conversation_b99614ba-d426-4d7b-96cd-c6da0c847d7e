import { Billing } from "cargo-api";
import type { ChartOptions } from "cargo-components/Chart";
import { Chart } from "cargo-components/Chart";
import { Spin } from "cargo-components/Spin";
import type { BillingTypes } from "cargo-domain-types";
import { LazyDatePicker } from "components/LazyDatePicker";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import moment from "moment";
import { useTheme } from "next-themes";
import React from "react";
import { useQuery } from "react-query";
import resolveConfig from "tailwindcss/resolveConfig";

import tailwindConfig from "../../../../../../tailwind.config";
import type { UsagePeriod, WorkflowStats } from "../types";
import { getUsagePeriodDates, numberFormatter } from "../utils";
import { SettingsUsageOrchestrationTable } from "./SettingsUsageOrchestrationTable/SettingsUsageOrchestrationTable";

const computedTailwindConfig = resolveConfig(tailwindConfig);

const chartColorMap: Record<
  string,
  Record<"ticks" | "grid" | "hoverBackgroundColor", string>
> = {
  light: {
    ticks: computedTailwindConfig.theme.colors.neutral[400],
    grid: `${computedTailwindConfig.theme.colors.neutral[400]}50`,
    hoverBackgroundColor: "transparent",
  },
  dark: {
    ticks: computedTailwindConfig.theme.colors.neutral[600],
    grid: computedTailwindConfig.theme.colors.neutral[800],
    hoverBackgroundColor: "transparent",
  },
};

export const SettingsUsageOrchestration: React.FC = () => {
  const { resolvedTheme: theme = "light" } = useTheme();

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);
  const { workspaceUuid } = React.useContext(WorkspaceContext);

  const defaultPeriod = React.useMemo<UsagePeriod>(() => {
    return getUsagePeriodDates();
  }, []);

  const [usagePeriod, setUsagePeriod] = React.useState<UsagePeriod | undefined>(
    undefined,
  );

  const { from, to } = React.useMemo(() => {
    if (usagePeriod !== undefined) {
      return getUsagePeriodDates(usagePeriod);
    }
    return defaultPeriod;
  }, [usagePeriod, defaultPeriod]);

  const chartColor = React.useMemo(() => {
    return chartColorMap[theme];
  }, [theme]);

  const getOrchestrationUsageMetricsPayload = React.useCallback(
    (unit: BillingTypes.UsageUnit): Billing.Api.UsageMetricsPayload => {
      return {
        from: moment(from).format("YYYY-MM-DD"),
        to: moment(to).format("YYYY-MM-DD"),
        workspaceUuid,
        unit,
        groupBy: "workflow_uuid",
      };
    },
    [from, to, workspaceUuid],
  );

  const getSuccessOrchestrationUsageMetricsState = useQuery(
    Billing.usagesKeys.getMetrics(
      workspaceUuid,
      getOrchestrationUsageMetricsPayload("orchestration.executions.success"),
    ),
    () =>
      api.billing.usages.getMetrics(
        getOrchestrationUsageMetricsPayload("orchestration.executions.success"),
      ),
    {
      onError: () => {
        addNotification({
          title: "An unknown error has occurred",
          type: "error",
        });
      },
      keepPreviousData: false,
    },
  );

  const getErrorOrchestrationUsageMetricsState = useQuery(
    Billing.usagesKeys.getMetrics(
      workspaceUuid,
      getOrchestrationUsageMetricsPayload("orchestration.executions.error"),
    ),
    () =>
      api.billing.usages.getMetrics(
        getOrchestrationUsageMetricsPayload("orchestration.executions.error"),
      ),
    {
      onError: () => {
        addNotification({
          title: "An unknown error has occurred",
          type: "error",
        });
      },
      keepPreviousData: false,
    },
  );

  const groupedMetrics = React.useMemo<
    Map<string, BillingTypes.UsageMetric[]>
  >(() => {
    const executionMap = new Map<string, BillingTypes.UsageMetric[]>();
    executionMap.set("success", []);
    executionMap.set("failure", []);

    if (getSuccessOrchestrationUsageMetricsState.isSuccess === true) {
      executionMap.set(
        "success",
        getSuccessOrchestrationUsageMetricsState.data.metrics,
      );
    }

    if (getErrorOrchestrationUsageMetricsState.isSuccess === true) {
      executionMap.set(
        "failure",
        getErrorOrchestrationUsageMetricsState.data.metrics,
      );
    }

    return executionMap;
  }, [
    getErrorOrchestrationUsageMetricsState.data?.metrics,
    getErrorOrchestrationUsageMetricsState.isSuccess,
    getSuccessOrchestrationUsageMetricsState.data?.metrics,
    getSuccessOrchestrationUsageMetricsState.isSuccess,
  ]);

  const chartData = React.useMemo(() => {
    const start = moment(from);
    const end = moment(to);

    const totalDays = end.diff(start, "days") + 1;
    const allDaysInPeriod = Array.from({ length: totalDays }, (_, index) => {
      const date = start.clone().add(index, "days");
      return {
        day: date.format("YYYY-MM-DD"),
        displayDay: date.format("DD"),
        success: 0,
        failure: 0,
      };
    });

    const metricsSuccess = groupedMetrics.get("success") ?? [];
    const metricsFailure = groupedMetrics.get("failure") ?? [];

    const isSameDay = (date1: string, date2: string) =>
      moment.utc(date1).isSame(moment.utc(date2), "day");

    for (const metric of metricsSuccess) {
      const day = moment.utc(metric.date).format("YYYY-MM-DD");
      const existingDay = allDaysInPeriod.find((period) =>
        isSameDay(period.day, day),
      );
      if (existingDay !== undefined) {
        existingDay.success += metric.items.reduce(
          (acc, item) => acc + (item.creditsUsedCount ?? 0),
          0,
        );
      }
    }

    for (const metric of metricsFailure) {
      const day = moment.utc(metric.date).format("YYYY-MM-DD");
      const existingDay = allDaysInPeriod.find((period) =>
        isSameDay(period.day, day),
      );
      if (existingDay !== undefined) {
        existingDay.failure += metric.items.reduce(
          (acc, item) => acc + (item.creditsUsedCount ?? 0),
          0,
        );
      }
    }

    return allDaysInPeriod;
  }, [from, to, groupedMetrics]);

  const chartConfig = React.useMemo(() => {
    const sortedChartData = chartData.sort(
      (a, b) => Number(a.day) - Number(b.day),
    );

    return {
      labels: sortedChartData.map((data) => data.displayDay),
      datasets: [
        {
          label: "Success",
          backgroundColor: "#34D399",
          data: sortedChartData.map((data) => data.success),
          stack: "workflow",
        },
        {
          label: "Failure",
          backgroundColor: "#F87171",
          data: sortedChartData.map((data) => data.failure),
          stack: "workflow",
        },
      ],
    };
  }, [chartData]);

  const chartOptions = React.useMemo<ChartOptions>(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      hoverBackgroundColor: "transparent",
      showTooltips: false,
      scales: {
        x: {
          stacked: true,
          grid: { display: false },
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
          },
        },
        y: {
          stacked: true,
          beginAtZero: true,
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
            callback: (value) => {
              return value.toLocaleString().replace(/,/g, " ");
            },
          },
          grid: {
            color: chartColor !== undefined ? chartColor.grid : undefined,
          },
          border: {
            display: false,
          },
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: false,
        },
      },
    };
  }, [chartColor]);

  const selectUsagePeriod = React.useCallback((dates: Date[] | undefined) => {
    if (dates === undefined) {
      setUsagePeriod(undefined);
      return;
    }

    const [from, to] = dates;

    if (from === undefined || to === undefined) {
      return;
    }

    setUsagePeriod({
      from,
      to,
    });
  }, []);

  const totalRunCount = React.useMemo(() => {
    let count = 0;

    const metricsSuccess = groupedMetrics.get("success") ?? [];
    for (const metric of metricsSuccess) {
      for (const item of metric.items) {
        count += item.creditsUsedCount ?? 0;
      }
    }

    const metricsError = groupedMetrics.get("failure") ?? [];
    for (const metric of metricsError) {
      for (const item of metric.items) {
        count += item.creditsUsedCount ?? 0;
      }
    }

    return count;
  }, [groupedMetrics]);

  const workflowsStats = React.useMemo<WorkflowStats[]>(() => {
    const statsMap = new Map<
      string,
      { uuid: string; success: number; error: number }
    >();

    const metricsSuccess = groupedMetrics.get("success") ?? [];
    for (const metric of metricsSuccess) {
      for (const item of metric.items) {
        if (statsMap.has(item.uuid) === false) {
          statsMap.set(item.uuid, { uuid: item.uuid, success: 0, error: 0 });
        }
        statsMap.get(item.uuid)!.success += item.creditsUsedCount ?? 0;
      }
    }

    const metricsError = groupedMetrics.get("failure") ?? [];
    for (const metric of metricsError) {
      for (const item of metric.items) {
        if (statsMap.has(item.uuid) === false) {
          statsMap.set(item.uuid, { uuid: item.uuid, success: 0, error: 0 });
        }
        statsMap.get(item.uuid)!.error += item.creditsUsedCount ?? 0;
      }
    }

    const rows = Array.from(statsMap.entries())
      .map(([uuid, stat]) => {
        const total = stat.success + stat.error;
        return {
          uuid,
          successCount: stat.success,
          errorCount: stat.error,
          total,
          successPercentage: total > 0 ? stat.success / total : 0,
          errorPercentage: total > 0 ? stat.error / total : 0,
        };
      })
      .sort((a, b) => b.total - a.total);

    const totalUsage = rows.reduce((acc, row) => acc + row.total, 0);

    return rows.map((row) => ({
      ...row,
      totalPercentageUsage: totalUsage > 0 ? row.total / totalUsage : 0,
    }));
  }, [groupedMetrics]);

  return (
    <div className="flex size-full flex-col items-stretch justify-start px-7 pt-7">
      <div className="mb-4 flex h-10 shrink-0 flex-row items-stretch justify-between">
        <div className="flex-1 font-medium lining-nums tabular-nums">
          {getSuccessOrchestrationUsageMetricsState.isLoading === true ||
          getErrorOrchestrationUsageMetricsState.isLoading === true
            ? 0
            : numberFormatter.format(totalRunCount)}{" "}
          steps executed
        </div>
        <div className="w-[275px] shrink-0">
          <LazyDatePicker
            dates={[from, to]}
            onChange={selectUsagePeriod}
            isRange={true}
          />
        </div>
      </div>
      {getErrorOrchestrationUsageMetricsState.isLoading === true ||
      getSuccessOrchestrationUsageMetricsState.isLoading === true ? (
        <Spin />
      ) : (
        <React.Fragment>
          <div className="h-50 w-full shrink-0">
            <Chart
              id="usage-orchestration"
              type="bar"
              options={chartOptions}
              data={chartConfig}
            />
          </div>
          <div className="mt-6 min-h-0 flex-1 overflow-y-auto pb-3">
            <SettingsUsageOrchestrationTable
              workflowsStats={workflowsStats}
              from={from}
              to={to}
            />
          </div>
        </React.Fragment>
      )}
    </div>
  );
};
