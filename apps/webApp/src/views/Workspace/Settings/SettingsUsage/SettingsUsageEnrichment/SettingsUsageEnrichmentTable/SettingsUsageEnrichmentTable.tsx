import type { Connection } from "cargo-api";
import { useStore } from "cargo-api";
import type { BillingTypes } from "cargo-domain-types";
import { useCountAnimation } from "cargo-react-utils";
import { motion } from "framer-motion";
import { IntegrationIcon } from "modules/Connection/IntegrationIcon";
import moment from "moment";
import React from "react";

import {
  getItemColor,
  listAnimationVariants,
  numberFormatter,
  percentageFormatter,
} from "../../utils";
import { colors } from "../utils";

type Props = {
  items: BillingTypes.UsageMetric["items"];
  from: Date;
  to: Date;
  totalCreditsCount: number;
  setIntegration: (integration: Connection.Integration) => void;
};

export const SettingsUsageEnrichmentTable: React.FC<Props> = (props) => {
  const { items, totalCreditsCount, from, to, setIntegration } = props;

  const integrations = useStore((state) => state.integrations);

  const formattedTitle = React.useMemo(() => {
    if (from !== undefined && to !== undefined) {
      return `${moment(from).format("MMM YYYY")} - ${moment(to).format("MMM YYYY")}`;
    }

    return moment(new Date()).format("MMM YYYY");
  }, [from, to]);

  const animatedTotalCreditsCount = useCountAnimation(totalCreditsCount);

  const animationKey = React.useMemo(
    () => `${from.toString()}-${to.toString()}`,
    [from, to],
  );

  return (
    <table className="min-w-full table-fixed text-sm font-normal leading-5 text-neutral-500 dark:text-neutral-400">
      <thead className="sticky top-0 z-layer-1 bg-white shadow-[0_1px_0px_0px_rgba(0,0,0,0.12)] dark:bg-neutral-900 dark:shadow-[0_1px_0px_0px_rgba(255,255,255,0.1)]">
        <tr className="py-2.5 text-sm font-medium leading-4 text-neutral-800 dark:text-neutral-100">
          <motion.th
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="py-2 text-left font-medium"
          >
            {formattedTitle}
          </motion.th>
          <th className="py-2 text-right font-medium">
            {numberFormatter.format(animatedTotalCreditsCount)} Credits
          </th>
          <th className="w-32 py-2 pr-4 text-right font-medium">100%</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colSpan={3} className="h-3"></td>
        </tr>
        {items.map((item, index) => {
          const percentage = item.creditsUsedCount / totalCreditsCount;
          const integration = integrations.find(
            (integration) => integration.slug === item.uuid,
          );

          if (integration === undefined) {
            return null;
          }

          return (
            <motion.tr
              key={`${item.uuid}-${animationKey}`}
              custom={index}
              animate="enter"
              initial="initial"
              exit="leave"
              variants={listAnimationVariants}
              className="w-full cursor-pointer transition-all duration-300 ease-in-out hover:bg-neutral-100 dark:hover:bg-neutral-800"
              onClick={() => {
                setIntegration(integration);
              }}
            >
              <td className="rounded-l-md py-2 pl-2">
                <div className="flex items-center">
                  <div
                    className="mr-3 size-1.5 rounded-full"
                    style={{
                      background: getItemColor(
                        items.map((item) => item.uuid),
                        item.uuid,
                        8,
                        colors,
                      ),
                    }}
                  />
                  <IntegrationIcon
                    slug={integration.slug}
                    size="small"
                    className="mr-2"
                  />
                  <span className="text-sm font-medium leading-5 text-neutral-800 dark:text-neutral-100">
                    {integration.name}
                  </span>
                </div>
              </td>

              <td className="py-2 text-right">
                {numberFormatter.format(item.creditsUsedCount)} Credits
              </td>

              <td className="rounded-r-md py-2 pr-3 text-right">
                {percentageFormatter.format(percentage)}
              </td>
            </motion.tr>
          );
        })}
      </tbody>
    </table>
  );
};
