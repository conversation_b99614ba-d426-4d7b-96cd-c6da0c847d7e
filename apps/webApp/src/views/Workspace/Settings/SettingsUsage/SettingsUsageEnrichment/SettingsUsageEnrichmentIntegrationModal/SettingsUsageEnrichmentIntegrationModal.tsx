import type { Connection } from "cargo-api";
import { Billing, useStore } from "cargo-api";
import { Modal } from "cargo-components/Modal";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Title } from "cargo-components/Title";
import type { BillingTypes } from "cargo-domain-types";
import {
  capitalizeFirstLetter,
  getEntries,
  getNumberWithMaxDecimals,
  groupBy,
} from "cargo-utils";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { motion } from "framer-motion";
import { IntegrationIcon } from "modules/Connection/IntegrationIcon";
import { PlayIntegrations } from "modules/Orchestration/PlayIntegrations/PlayIntegrations";
import { ToolIcon } from "modules/Orchestration/ToolIcon";
import { ModelIcon } from "modules/Storage/ModelIcon/ModelIcon";
import moment from "moment";
import React from "react";
import { useQuery } from "react-query";

import type { ItemUsage } from "../../types";
import { listAnimationVariants, percentageFormatter } from "../../utils";

type Props = {
  workspaceUuid: string;
  integration?: Connection.Integration;
  from: Date;
  to: Date;
  isOpen: boolean;
  onCancel: () => void;
};

export const SettingsUsageEnrichmentIntegrationModal: React.FC<Props> = (
  props,
) => {
  const { workspaceUuid, integration, from, to, isOpen, onCancel } = props;

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);

  const workflows = useStore((state) => state.workflows);
  const plays = useStore((state) => state.plays);
  const tools = useStore((state) => state.tools);
  const models = useStore((state) => state.models);

  const getUsageMetricsPayload = React.useCallback(
    (
      groupBy: Billing.Api.UsageMetricsPayload["groupBy"],
    ): Billing.Api.UsageMetricsPayload => {
      return {
        from: moment(from).utc().format("YYYY-MM-DD"),
        to: moment(to).utc().format("YYYY-MM-DD"),
        workspaceUuid,
        integrationSlug: integration?.slug,
        unit: "integration.credits",
        groupBy,
      };
    },
    [integration?.slug, from, to, workspaceUuid],
  );

  const getConectorModelsState = useQuery(
    Billing.usagesKeys.getMetrics(
      workspaceUuid,
      getUsageMetricsPayload("model_uuid"),
    ),
    () => api.billing.usages.getMetrics(getUsageMetricsPayload("model_uuid")),
    {
      enabled: integration !== undefined,
      keepPreviousData: false,
      onError: () => {
        addNotification({
          title: "Cannot get integration",
          type: "error",
        });
      },
    },
  );

  const getConectorWorkflowsState = useQuery(
    Billing.usagesKeys.getMetrics(
      workspaceUuid,
      getUsageMetricsPayload("workflow_uuid"),
    ),
    () =>
      api.billing.usages.getMetrics(getUsageMetricsPayload("workflow_uuid")),
    {
      enabled: integration !== undefined,
      keepPreviousData: false,
      onError: () => {
        addNotification({
          title: "Cannot get plays and tools",
          type: "error",
        });
      },
    },
  );

  const metrics = React.useMemo<BillingTypes.UsageMetric[]>(() => {
    const result: BillingTypes.UsageMetric[] = [];

    if (getConectorModelsState.isSuccess === true) {
      result.push(...getConectorModelsState.data.metrics);
    }

    if (getConectorWorkflowsState.isSuccess === true) {
      result.push(...getConectorWorkflowsState.data.metrics);
    }

    return result;
  }, [
    getConectorModelsState.data?.metrics,
    getConectorModelsState.isSuccess,
    getConectorWorkflowsState.data?.metrics,
    getConectorWorkflowsState.isSuccess,
  ]);

  const items = React.useMemo<BillingTypes.UsageMetric["items"]>(() => {
    const result = new Map<string, BillingTypes.UsageMetric["items"][0]>();

    for (const metric of metrics) {
      for (const item of metric.items) {
        if (result.has(item.uuid)) {
          result.get(item.uuid)!.creditsUsedCount += item.creditsUsedCount;
        } else {
          result.set(item.uuid, { ...item });
        }
      }
    }

    return Array.from(result.values());
  }, [metrics]);

  const getItem = React.useCallback(
    (uuid: string): ItemUsage | undefined => {
      const workflow = workflows.find((workflow) => workflow.uuid === uuid);

      if (workflow !== undefined && workflow.playUuid !== undefined) {
        const play = plays.find((play) => play.uuid === workflow.playUuid);
        if (play !== undefined) {
          return { kind: "play", item: play };
        }
      }

      if (workflow !== undefined && workflow.toolUuid !== undefined) {
        const tool = tools.find((tool) => tool.uuid === workflow.toolUuid);
        if (tool !== undefined) {
          return { kind: "tool", item: tool };
        }
      }

      const model = models.find((model) => model.uuid === uuid);

      if (model !== undefined) {
        return { kind: "model", item: model };
      }

      return undefined;
    },
    [models, plays, tools, workflows],
  );

  const enrichedItems = React.useMemo(() => {
    return items
      .map((item) => {
        const found = getItem(item.uuid);
        if (found === undefined) {
          return null;
        }
        return { ...item, kind: found.kind };
      })
      .filter(Boolean) as ((typeof items)[0] & { kind: string })[];
  }, [items, getItem]);

  const totalCreditsCount = React.useMemo(() => {
    return items.reduce((total, item) => {
      return total + item.creditsUsedCount;
    }, 0);
  }, [items]);

  const groupedItems = React.useMemo(() => {
    return groupBy(enrichedItems, "kind");
  }, [enrichedItems]);

  return (
    <Modal
      isOpen={isOpen}
      onCancel={onCancel}
      onGoBack={onCancel}
      hideHeader={true}
      className="h-[80vh] w-[600px] max-w-[90vw]"
    >
      <ScreenBody
        size="middle"
        rawTitle={
          integration !== undefined ? (
            <div className="flex flex-1 flex-row items-center space-x-2">
              <IntegrationIcon slug={integration.slug} size="small" />
              <Title level={3}>
                &quot;{integration.name}&quot; integration
              </Title>
            </div>
          ) : null
        }
        className="flex h-full flex-row overflow-hidden"
      >
        <div className="flex flex-1 flex-col overflow-hidden">
          <div className="-mt-px flex-1 overflow-auto">
            <table className="min-w-full table-fixed text-sm font-normal leading-5 text-neutral-500 dark:text-neutral-400">
              <tbody>
                {getEntries(groupedItems).map(([category, items], index) => {
                  const isLastSection =
                    index === getEntries(groupedItems).length - 1;
                  return (
                    <React.Fragment key={category}>
                      <tr>
                        <td
                          colSpan={3}
                          className="px-2 pt-2 text-sm font-medium leading-6"
                        >
                          {capitalizeFirstLetter(`${category}s`)}
                        </td>
                      </tr>
                      {items.map((item) => {
                        const percentage =
                          item.creditsUsedCount / totalCreditsCount;
                        const currentItem = getItem(item.uuid);

                        if (currentItem === undefined) {
                          return null;
                        }

                        return (
                          <motion.tr
                            key={item.uuid}
                            className="w-full"
                            custom={index}
                            animate="enter"
                            initial="initial"
                            exit="leave"
                            variants={listAnimationVariants}
                          >
                            <td className="p-2">
                              <div className="flex items-center">
                                {currentItem.kind === "play" ? (
                                  <PlayIntegrations
                                    play={currentItem.item}
                                    size="small"
                                    className="mr-2"
                                  />
                                ) : null}
                                {currentItem.kind === "tool" ? (
                                  <ToolIcon
                                    tool={currentItem.item}
                                    size="small"
                                    className="mr-2"
                                  />
                                ) : null}
                                {currentItem.kind === "model" ? (
                                  <ModelIcon
                                    model={currentItem.item}
                                    size="small"
                                    className="mr-2"
                                  />
                                ) : null}
                                <span className="line-clamp-1 text-sm font-medium leading-5 text-neutral-800 dark:text-neutral-100">
                                  {currentItem.item.name}
                                </span>
                              </div>
                            </td>

                            <td className="truncate py-2 text-right">
                              {getNumberWithMaxDecimals(
                                item.creditsUsedCount,
                                2,
                              )}{" "}
                              Credits
                            </td>

                            <td className="py-2 pl-4 pr-2 text-right">
                              {percentageFormatter.format(percentage)}
                            </td>
                          </motion.tr>
                        );
                      })}
                      {isLastSection === false ? (
                        <tr className="shadow-[0_1px_0px_0px_rgba(0,0,0,0.12)] dark:bg-neutral-900 dark:shadow-[0_1px_0px_0px_rgba(255,255,255,0.1)]">
                          <td colSpan={3} className="h-3"></td>
                        </tr>
                      ) : null}
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </ScreenBody>
    </Modal>
  );
};
