import type { Connection } from "cargo-api";
import { Billing } from "cargo-api";
import type { ChartOptions } from "cargo-components/Chart";
import { Chart } from "cargo-components/Chart";
import { Spin } from "cargo-components/Spin";
import type { BillingTypes } from "cargo-domain-types";
import { LazyDatePicker } from "components/LazyDatePicker";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import moment from "moment";
import { useTheme } from "next-themes";
import React from "react";
import { useQuery } from "react-query";
import resolveConfig from "tailwindcss/resolveConfig";

import tailwindConfig from "../../../../../../tailwind.config";
import type { ChartData, UsagePeriod } from "../types";
import { getItemColor, getUsagePeriodDates, numberFormatter } from "../utils";
import { SettingsUsageEnrichmentIntegrationModal } from "./SettingsUsageEnrichmentIntegrationModal/SettingsUsageEnrichmentIntegrationModal";
import { SettingsUsageEnrichmentTable } from "./SettingsUsageEnrichmentTable/SettingsUsageEnrichmentTable";
import { colors } from "./utils";

const computedTailwindConfig = resolveConfig(tailwindConfig);

const chartColorMap: Record<
  string,
  Record<"ticks" | "grid" | "hoverBackgroundColor", string>
> = {
  light: {
    ticks: computedTailwindConfig.theme.colors.neutral[400],
    grid: `${computedTailwindConfig.theme.colors.neutral[400]}50`,
    hoverBackgroundColor: `${computedTailwindConfig.theme.colors.neutral[200]}80`,
  },
  dark: {
    ticks: computedTailwindConfig.theme.colors.neutral[600],
    grid: computedTailwindConfig.theme.colors.neutral[800],
    hoverBackgroundColor: computedTailwindConfig.theme.colors.neutral[800],
  },
};

export const SettingsUsageEnrichment: React.FC = () => {
  const { resolvedTheme: theme = "light" } = useTheme();

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);
  const { workspaceUuid } = React.useContext(WorkspaceContext);

  const defaultPeriod = React.useMemo<UsagePeriod>(() => {
    return getUsagePeriodDates();
  }, []);

  const [usagePeriod, setUsagePeriod] = React.useState<UsagePeriod | undefined>(
    undefined,
  );

  const { from, to } = React.useMemo(() => {
    if (usagePeriod !== undefined) {
      return getUsagePeriodDates(usagePeriod);
    }
    return defaultPeriod;
  }, [usagePeriod, defaultPeriod]);

  const [integration, setIntegration] = React.useState<
    Connection.Integration | undefined
  >(undefined);

  const chartColor = React.useMemo(() => {
    return chartColorMap[theme];
  }, [theme]);

  const usageMetricsPayload =
    React.useMemo<Billing.Api.UsageMetricsPayload>(() => {
      return {
        from: moment(from).format("YYYY-MM-DD"),
        to: moment(to).format("YYYY-MM-DD"),
        workspaceUuid,
        unit: "integration.credits",
        groupBy: "integration_slug",
      };
    }, [from, to, workspaceUuid]);

  const getUsageMetricsState = useQuery(
    Billing.usagesKeys.getMetrics(workspaceUuid, usageMetricsPayload),
    () => api.billing.usages.getMetrics(usageMetricsPayload),
    {
      onError: () => {
        addNotification({
          title: "An unknown error has occurred",
          type: "error",
        });
      },
      keepPreviousData: false,
    },
  );

  const metrics = React.useMemo(() => {
    if (getUsageMetricsState.isSuccess === false) {
      return [];
    }

    const metrics = getUsageMetricsState.data.metrics;
    return metrics;
  }, [getUsageMetricsState.data?.metrics, getUsageMetricsState.isSuccess]);

  const chartData = React.useMemo(() => {
    const totalConsumptionByIntegration: Record<string, number> = {};

    const start = moment.utc(from);
    const end = moment.utc(to);

    const totalDays = end.diff(start, "days") + 1;

    const allDaysInPeriod = Array.from({ length: totalDays }, (_, index) => {
      const date = start.clone().add(index, "days");
      return {
        day: date.format("YYYY-MM-DD"),
        displayDay: date.format("DD"),
      };
    });

    const chartData: ChartData[] = [...allDaysInPeriod];

    for (const metric of metrics) {
      const day = moment.utc(metric.date).format("YYYY-MM-DD");

      const existingDayIndex = chartData.findIndex(
        (dataItem) => dataItem.day === day,
      );

      const integrationsData: Record<string, number> = {};

      for (const integration of metric.items) {
        totalConsumptionByIntegration[integration.uuid] =
          (totalConsumptionByIntegration[integration.uuid] || 0) +
          integration.creditsUsedCount;
        integrationsData[integration.uuid] = integration.creditsUsedCount;
      }

      if (existingDayIndex > -1) {
        chartData[existingDayIndex] = {
          ...chartData[existingDayIndex],
          ...integrationsData,
        } as ChartData;
      }
    }

    return chartData;
  }, [from, metrics, to]);

  const integrations = React.useMemo<BillingTypes.UsageMetric["items"]>(() => {
    const result = new Map<string, BillingTypes.UsageMetric["items"][0]>();

    for (const metric of metrics) {
      const metricDay = moment(metric.date).format("YYYY-MM-DD");
      const fromDay = moment(from).format("YYYY-MM-DD");
      const toDay = moment(to).format("YYYY-MM-DD");

      if (metricDay >= fromDay && metricDay <= toDay) {
        for (const item of metric.items) {
          if (result.has(item.uuid)) {
            result.get(item.uuid)!.creditsUsedCount += item.creditsUsedCount;
          } else {
            result.set(item.uuid, { ...item });
          }
        }
      }
    }

    const sortedIntegrations = Array.from(result.values()).sort((a, b) => {
      const integrationATotalConsumption = a.creditsUsedCount || 0;
      const integrationBTotalConsumption = b.creditsUsedCount || 0;

      return integrationBTotalConsumption - integrationATotalConsumption;
    });

    return sortedIntegrations;
  }, [from, to, metrics]);

  const totalCreditsCount = React.useMemo(() => {
    return integrations.reduce((total, integration) => {
      return total + integration.creditsUsedCount;
    }, 0);
  }, [integrations]);

  const selectUsagePeriod = React.useCallback((dates: Date[] | undefined) => {
    if (dates === undefined) {
      setUsagePeriod(undefined);
      return;
    }

    const [from, to] = dates;

    if (from === undefined || to === undefined) {
      return;
    }

    setUsagePeriod({
      from,
      to,
    });
  }, []);

  const chartConfig = React.useMemo(() => {
    const sortedChartData = chartData.sort(
      (a, b) => Number(a.day) - Number(b.day),
    );
    return {
      labels: chartData.map((data) => data.displayDay),
      datasets: integrations.map((integration) => ({
        label: integration.uuid,
        backgroundColor: getItemColor(
          integrations.map((integration) => integration.uuid),
          integration.uuid,
          8,
          colors,
        ),
        data: sortedChartData.map((data) => Number(data[integration.uuid])),
        stack: "integration",
      })),
    };
  }, [chartData, integrations]);

  const chartOptions = React.useMemo<ChartOptions>(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      hoverBackgroundColor: "transparent",
      showTooltips: false,
      scales: {
        x: {
          stacked: true,
          grid: { display: false },
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
          },
        },
        y: {
          stacked: true,
          beginAtZero: true,
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
            callback: (value) => {
              return value.toLocaleString().replace(/,/g, " ");
            },
          },
          grid: {
            color: chartColor !== undefined ? chartColor.grid : undefined,
          },
          border: {
            display: false,
          },
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: false,
        },
      },
    };
  }, [chartColor]);

  return (
    <React.Fragment>
      <SettingsUsageEnrichmentIntegrationModal
        workspaceUuid={workspaceUuid}
        from={from}
        to={to}
        isOpen={integration !== undefined}
        integration={integration}
        onCancel={() => {
          setIntegration(undefined);
        }}
      />
      <div className="flex size-full flex-col items-stretch justify-start px-7 pt-7">
        <div className="mb-4 flex shrink-0 flex-row items-stretch justify-between">
          <div className="flex-1">
            <div className="font-medium lining-nums tabular-nums">
              {getUsageMetricsState.isLoading === true
                ? 0
                : numberFormatter.format(totalCreditsCount)}{" "}
              credits
            </div>
            <div className="text-neutral-500 dark:text-neutral-400">
              spent for enrichment
            </div>
          </div>
          <div className="w-[275px] shrink-0">
            <LazyDatePicker
              dates={[from, to]}
              onChange={selectUsagePeriod}
              isRange={true}
            />
          </div>
        </div>
        {getUsageMetricsState.isLoading === true ? (
          <Spin />
        ) : (
          <React.Fragment>
            <div className="h-50 w-full shrink-0">
              <Chart
                id="usage-enrichment"
                type="bar"
                options={chartOptions}
                data={chartConfig}
              />
            </div>
            <div className="mt-6 min-h-0 flex-1 overflow-y-auto pb-3">
              <SettingsUsageEnrichmentTable
                items={integrations}
                from={from}
                to={to}
                totalCreditsCount={totalCreditsCount}
                setIntegration={setIntegration}
              />
            </div>
          </React.Fragment>
        )}
      </div>
    </React.Fragment>
  );
};
