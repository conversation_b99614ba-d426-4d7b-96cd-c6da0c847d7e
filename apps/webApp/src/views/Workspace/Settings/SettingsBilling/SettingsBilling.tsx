import { AiOutlineDownload } from "@react-icons/all-files/ai/AiOutlineDownload";
import { LuArrowUpRight } from "@react-icons/all-files/lu/LuArrowUpRight";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { Billing, determineIfIsFetcherError, useStore } from "cargo-api";
import { Alert } from "cargo-components/Alert";
import { Button } from "cargo-components/Button";
import { CheckboxItem } from "cargo-components/Checkbox/CheckboxItem";
import { Empty } from "cargo-components/Empty";
import { List } from "cargo-components/List";
import { ListItem } from "cargo-components/List/ListItem";
import { ScreenBody } from "cargo-components/Screen/ScreenBody";
import { Spin } from "cargo-components/Spin";
import { Title } from "cargo-components/Title";
import {
  formatCredits,
  formatPrice,
  getNumberWithMaxDecimals,
} from "cargo-utils";
import { LazyTooltip } from "components/LazyTooltip";
import { config } from "config";
import { useAnalytics } from "contexts/AnalyticsContext/useAnalytics";
import { ApiContext } from "contexts/ApiContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import { format } from "date-fns";
import { CreditsUpdateBalanceModal } from "modules/Billing/CreditsUsage/CreditsUpdateBalanceModal";
import { CreditsUpdateTopupModal } from "modules/Billing/CreditsUsage/CreditsUpdateTopupModal";
import { UpdatePaymentMethodModal } from "modules/Billing/CreditsUsage/UpdatePaymentMethodModal";
import moment from "moment";
import { useTheme } from "next-themes";
import React from "react";
import { useQuery } from "react-query";
import { useIntercom } from "react-use-intercom";
import resolveConfig from "tailwindcss/resolveConfig";

import tailwindConfig from "../../../../../tailwind.config";

const computedTailwindConfig = resolveConfig(tailwindConfig);

const stripePromise = loadStripe(config.stripe.publicKey);

const cadenceToPeriod: Record<Billing.SubscriptionCadence, string> = {
  monthly: "month",
  annual: "year",
  one_time: "lifetime",
  quarterly: "quarter",
  semi_annual: "6 months",
  custom: "period",
};

const COLUMNS = [
  { value: "id", label: "ID" },
  { value: "amount", label: "Amount" },
  { value: "dueDate", label: "Due Date" },
  { value: "isPaid", label: "Is Paid" },
  { value: "link", label: "Link" },
];

export const SettingsBilling: React.FC = () => {
  const { api } = React.useContext(ApiContext);
  const { workspaceUuid } = React.useContext(WorkspaceContext);

  const { resolvedTheme: theme } = useTheme();
  const { track } = useAnalytics();
  const { show: showIntercom } = useIntercom();

  const subscription = useStore((state) => state.subscription);

  const [isCreditsUpdateBalanceModalOpen, setIsCreditsUpdateBalanceModalOpen] =
    React.useState<boolean>(false);

  const [isCreditsUpdateTopupModalOpen, setIsCreditsUpdateTopupModalOpen] =
    React.useState<boolean>(false);

  const [isUpdatePaymentMethodModalOpen, setIsUpdatePaymentMethodModalOpen] =
    React.useState<boolean>(false);

  const billSetupIntentState = useQuery(
    Billing.subscriptionsKeys.createSetupIntent(workspaceUuid),
    api.billing.subscriptions.createSetupIntent,
  );

  const billInvoicesState = useQuery(
    Billing.subscriptionsKeys.getInvoices(workspaceUuid),
    api.billing.subscriptions.getInvoices,
  );

  const billCreditCardState = useQuery(
    Billing.subscriptionsKeys.getCreditCard(workspaceUuid),
    api.billing.subscriptions.getCreditCard,
  );

  const price = React.useMemo(() => {
    if (subscription === undefined) {
      return 0;
    }

    return getNumberWithMaxDecimals(
      subscription.subscriptionAvailableCreditsCount *
        subscription.conversionRate,
      2,
    );
  }, [subscription]);

  const futurePrice = React.useMemo(() => {
    if (subscription === undefined || subscription.futureChange === undefined) {
      return undefined;
    }

    return getNumberWithMaxDecimals(
      subscription.futureChange.creditsCount *
        subscription.futureChange.conversionRate,
      2,
    );
  }, [subscription]);

  const handleSubscription = React.useCallback(async () => {
    track("link_clicked", {
      link_name: "manage_subscription",
      workspace_uuid: workspaceUuid,
    });

    const { portalSession } =
      await api.billing.subscriptions.createPortalSession();

    window.open(portalSession.url, "_blank");
  }, [api.billing.subscriptions, track, workspaceUuid]);

  if (
    billSetupIntentState.isLoading === true ||
    billInvoicesState.isLoading === true ||
    billCreditCardState.isLoading === true
  ) {
    return <Spin />;
  }

  if (
    subscription === undefined ||
    billSetupIntentState.isSuccess === false ||
    billInvoicesState.isSuccess === false ||
    billCreditCardState.isSuccess === false
  ) {
    return (
      <Empty
        title="Unable to retrieve billing details"
        description={
          determineIfIsFetcherError(billInvoicesState.error)
            ? billInvoicesState.error.message
            : "Unknown error"
        }
      />
    );
  }

  const { setupIntent } = billSetupIntentState.data;
  const { invoices } = billInvoicesState.data;
  const { creditCard } = billCreditCardState.data;

  return (
    <React.Fragment>
      <CreditsUpdateBalanceModal
        subscription={subscription}
        isOpen={isCreditsUpdateBalanceModalOpen}
        onSuccess={() => {
          setIsCreditsUpdateBalanceModalOpen(false);
        }}
        onClose={() => {
          setIsCreditsUpdateBalanceModalOpen(false);
        }}
      />
      <CreditsUpdateTopupModal
        subscription={subscription}
        isOpen={isCreditsUpdateTopupModalOpen}
        onSuccess={() => setIsCreditsUpdateTopupModalOpen(false)}
        onClose={() => setIsCreditsUpdateTopupModalOpen(false)}
      />
      <ScreenBody
        title="Billing"
        size="large"
        className="flex h-full flex-col overflow-auto"
        header={
          <Button
            type="primary"
            icon={<LuArrowUpRight />}
            text="Open Customer Portal"
            onClick={handleSubscription}
          />
        }
      >
        <Elements
          stripe={stripePromise}
          options={{
            clientSecret: setupIntent.clientSecret,
            appearance: {
              theme: theme === "dark" ? "night" : "stripe",
              disableAnimations: true,
              variables: {
                colorBackground:
                  theme === "dark"
                    ? computedTailwindConfig.theme.colors.stone[900]
                    : computedTailwindConfig.theme.colors.neutral[50],
              },
            },
          }}
        >
          {subscription.provider === "orb" ? (
            <div className="max-w-2xl space-y-7 p-7">
              {subscription.futureChange !== undefined ? (
                <Alert
                  type="warning"
                  title="Future subscription change"
                  className="rounded-md"
                  withIcon={false}
                >
                  <p>
                    The following plan update will be applied at{" "}
                    {format(
                      new Date(subscription.futureChange.startAt),
                      "MMM d, yyyy",
                    )}
                    :
                  </p>

                  <ul>
                    <li>
                      - Billing cadence: {subscription.cadence}
                      {subscription.futureChange.cadence !==
                      subscription.cadence ? (
                        <React.Fragment>
                          {" "}
                          =&gt; {subscription.futureChange.cadence}
                        </React.Fragment>
                      ) : null}
                    </li>

                    <li>
                      - Credits:{" "}
                      {formatCredits(
                        subscription.subscriptionAvailableCreditsCount,
                      )}{" "}
                      per {cadenceToPeriod[subscription.cadence]}
                      {subscription.futureChange.creditsCount !==
                      subscription.subscriptionAvailableCreditsCount ? (
                        <React.Fragment>
                          {" "}
                          =&gt;{" "}
                          {formatCredits(
                            subscription.futureChange.creditsCount,
                          )}{" "}
                          per{" "}
                          {cadenceToPeriod[subscription.futureChange.cadence]}
                        </React.Fragment>
                      ) : null}
                    </li>
                    <li>
                      - Fixed fee price: {formatPrice(price)} per{" "}
                      {cadenceToPeriod[subscription.cadence]}
                      {futurePrice !== undefined && futurePrice !== price ? (
                        <React.Fragment>
                          {" "}
                          =&gt; {formatPrice(futurePrice)} per{" "}
                          {cadenceToPeriod[subscription.futureChange.cadence]}
                        </React.Fragment>
                      ) : null}
                    </li>
                    <li>
                      - Price per credit:{" "}
                      {formatPrice(subscription.conversionRate)}
                      {subscription.conversionRate !==
                      subscription.futureChange.conversionRate ? (
                        <React.Fragment>
                          {" "}
                          =&gt;{" "}
                          {formatPrice(
                            subscription.futureChange.conversionRate,
                          )}
                        </React.Fragment>
                      ) : null}
                    </li>
                  </ul>
                </Alert>
              ) : null}

              <div className="flex items-start justify-between">
                <div>
                  <span className="font-medium">
                    {subscription !== undefined
                      ? `${formatCredits(subscription.subscriptionAvailableCreditsCount - subscription.subscriptionCreditsUsedCount)}/${formatCredits(subscription.subscriptionAvailableCreditsCount)} credits left`
                      : "No available"}
                  </span>

                  <p className="text-neutral-500 dark:text-neutral-400">
                    Current plan:{" "}
                    {formatPrice(
                      subscription.subscriptionAvailableCreditsCount *
                        subscription.conversionRate,
                    )}
                    <React.Fragment>
                      /{cadenceToPeriod[subscription.cadence]}
                    </React.Fragment>
                    {subscription.endAt !== undefined ? (
                      <React.Fragment>
                        , period renew on{" "}
                        {format(new Date(subscription.endAt), "MMM d, yyyy")}
                      </React.Fragment>
                    ) : null}
                  </p>
                </div>

                {subscription.plan === "self-serve" ? (
                  <React.Fragment>
                    <LazyTooltip
                      placement="right"
                      text={
                        (creditCard === undefined
                          ? "A payment method must be set before updating your plan"
                          : undefined) ||
                        (subscription.hasPendingChange === true
                          ? "An update of your subscription is already in progress. It can take a minute."
                          : undefined)
                      }
                    >
                      <Button
                        type="primary"
                        isDisabled={
                          creditCard === undefined ||
                          subscription.hasPendingChange === true
                        }
                        icon={<LuArrowUpRight />}
                        text="Update plan"
                        onClick={() => {
                          setIsCreditsUpdateBalanceModalOpen(true);
                        }}
                      />
                    </LazyTooltip>
                  </React.Fragment>
                ) : (
                  <Button
                    type="secondary"
                    icon={<LuArrowUpRight />}
                    text="Contact sales"
                    onClick={showIntercom}
                  />
                )}
              </div>

              <hr className="border-neutral-300/50 dark:border-neutral-700/50" />

              <div className="flex items-center justify-between">
                <div>
                  <span className="font-medium">Automatic top-up</span>
                  <p className="text-neutral-500 dark:text-neutral-400">
                    {subscription.topup
                      ? `Enabled: add ${formatCredits(subscription.topup?.creditsCount)} credits once balance has ${formatCredits(subscription.topup?.threshold)} credits left`
                      : "Disabled"}
                  </p>
                </div>

                <LazyTooltip
                  placement="right"
                  text={
                    creditCard === undefined
                      ? "A payment method must be set before updating your automatic top-up"
                      : undefined
                  }
                >
                  <Button
                    type="secondary"
                    isDisabled={creditCard === undefined}
                    icon={<LuArrowUpRight />}
                    text="Update top-up"
                    onClick={() => setIsCreditsUpdateTopupModalOpen(true)}
                  />
                </LazyTooltip>
              </div>

              <hr className="border-neutral-300/50 dark:border-neutral-700/50" />

              <div className="flex items-center justify-between">
                <div>
                  <span className="font-medium">Payment method</span>
                  <p className="text-neutral-500 dark:text-neutral-400">
                    {creditCard !== undefined
                      ? `Registered: card ending with ${creditCard.last4}, ${creditCard.expYear}/${creditCard.expMonth}`
                      : "No payment method registered"}
                  </p>
                </div>
                <Button
                  type="secondary"
                  icon={<LuArrowUpRight />}
                  text={
                    creditCard !== undefined
                      ? "Update payment method"
                      : "Set a payment method"
                  }
                  onClick={() => setIsUpdatePaymentMethodModalOpen(true)}
                />
              </div>
              <UpdatePaymentMethodModal
                isOpen={isUpdatePaymentMethodModalOpen}
                onSuccess={() => setIsUpdatePaymentMethodModalOpen(false)}
                onClose={() => setIsUpdatePaymentMethodModalOpen(false)}
              />
            </div>
          ) : null}

          {subscription?.provider === "orb" ? (
            <div className="mt-12 w-full shrink-0">
              <div className="flex w-full flex-row items-center justify-start space-x-2 border-t border-neutral-300/50 dark:border-neutral-700/50">
                <div className="mb-4 mt-4 flex h-8 min-w-0 flex-1 items-center justify-start space-x-2 p-7">
                  <Title level={1} className="w-full truncate">
                    Invoices
                  </Title>
                </div>
              </div>
            </div>
          ) : null}

          <div className="min-h-0 w-full flex-1">
            <List
              size="large"
              columns={COLUMNS}
              className="border-t border-neutral-300/50 dark:border-neutral-700/50"
            >
              {invoices.map((invoice, index) => (
                <ListItem
                  key={invoice.id}
                  index={index}
                  item={{
                    id: invoice.id,
                    amount: formatPrice(invoice.amount),
                    dueDate: invoice.dueDate
                      ? moment(invoice.dueDate).format(
                          "MMM Do, YYYY [at] hh:mm A",
                        )
                      : "-",
                    isPaid: <CheckboxItem isChecked={invoice.isPaid} />,
                    link: (
                      <Button
                        type="secondary"
                        isDisabled={!invoice.url}
                        icon={<AiOutlineDownload />}
                        onClick={() => {
                          window.open(invoice.url, "_blank");
                        }}
                      />
                    ),
                  }}
                />
              ))}
            </List>
          </div>
        </Elements>
      </ScreenBody>
    </React.Fragment>
  );
};
