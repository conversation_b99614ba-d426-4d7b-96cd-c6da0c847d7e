import { SystemOfRecordIntegration, useStore } from "cargo-api";
import type { ChartOptions } from "cargo-components/Chart";
import { Chart } from "cargo-components/Chart";
import { MonthPicker } from "cargo-components/MonthPicker";
import { Spin } from "cargo-components/Spin";
import { getBytesWithUnitString, getUTCDateString } from "cargo-utils";
import { ApiContext } from "contexts/ApiContext";
import { NotificationContext } from "contexts/NotificationContext";
import { WorkspaceContext } from "contexts/WorkspaceContext";
import { formatDuration, intervalToDuration } from "date-fns";
import { AnimatePresence, motion } from "framer-motion";
import moment from "moment";
import { useTheme } from "next-themes";
import React, { useMemo } from "react";
import { useQuery } from "react-query";
import resolveConfig from "tailwindcss/resolveConfig";
import { useDebounce } from "use-debounce";

import tailwindConfig from "../../../../../../tailwind.config";

const computedTailwindConfig = resolveConfig(tailwindConfig);

const chartColorMap: Record<
  string,
  Record<"ticks" | "grid" | "hoverBackgroundColor", string>
> = {
  light: {
    ticks: computedTailwindConfig.theme.colors.neutral[400],
    grid: `${computedTailwindConfig.theme.colors.neutral[400]}50`,
    hoverBackgroundColor: `${computedTailwindConfig.theme.colors.neutral[200]}80`,
  },
  dark: {
    ticks: computedTailwindConfig.theme.colors.neutral[600],
    grid: computedTailwindConfig.theme.colors.neutral[800],
    hoverBackgroundColor: computedTailwindConfig.theme.colors.neutral[800],
  },
};

type ChartData = {
  day: string;
  [key: string]: string | number;
};

export const SettingsSorMetrics: React.FC = () => {
  const { resolvedTheme: theme = "light" } = useTheme();

  const { addNotification } = React.useContext(NotificationContext);
  const { api } = React.useContext(ApiContext);
  const { workspaceUuid } = React.useContext(WorkspaceContext);

  const systemOfRecord = useStore((state) => state.systemOfRecord);

  const [selectedDate, setSelectedDate] = React.useState<Date>(new Date());
  const [debouncedDate] = useDebounce(selectedDate, 500);

  const getLogMetricsState = useQuery(
    SystemOfRecordIntegration.usagesKeys.getMetrics(workspaceUuid, {
      from: getUTCDateString(
        moment(debouncedDate).startOf("month").toDate(),
      ) as string,
      to: getUTCDateString(
        moment(debouncedDate).endOf("month").toDate(),
      ) as string,
    }),
    () =>
      api.systemOfRecordIntegration.log.getMetrics({
        from: getUTCDateString(
          moment(debouncedDate).startOf("month").toDate(),
        ) as string,
        to: getUTCDateString(
          moment(debouncedDate).endOf("month").toDate(),
        ) as string,
      }),
    {
      onError: () => {
        addNotification({
          title: "An unknown error has occurred",
          type: "error",
        });
      },
    },
  );

  const formattedMetricText = React.useMemo(() => {
    if (getLogMetricsState.status !== "success") {
      return 0;
    }

    if (systemOfRecord !== undefined && systemOfRecord.kind === "bigQuery") {
      const totalProcessedBytesCount = getLogMetricsState.data.metrics.reduce(
        (sum, current) => sum + current.processedBytesCount,
        0,
      );

      return getBytesWithUnitString(totalProcessedBytesCount);
    }

    const totalProcessedMillisecondsCount =
      getLogMetricsState.data.metrics.reduce(
        (sum, current) => sum + current.processedSecondsCount,
        0,
      ) * 1000;

    const duration = intervalToDuration({
      start: 0,
      end: totalProcessedMillisecondsCount,
    });

    return formatDuration(duration);
  }, [getLogMetricsState, systemOfRecord]);

  const chartColor = useMemo(() => {
    return chartColorMap[theme];
  }, [theme]);

  const { chartData } = useMemo(() => {
    if (getLogMetricsState.status !== "success") {
      return { chartData: [] };
    }

    const { metrics } = getLogMetricsState.data;

    const daysInMonth = moment(selectedDate).daysInMonth();

    const allDaysInMonth = new Array(daysInMonth)
      .fill(undefined)
      .map((_, index) => {
        const day = String(index + 1).padStart(2, "0");
        return {
          day,
        };
      });

    const chartData: ChartData[] = [...allDaysInMonth];

    for (const metric of metrics) {
      const day = String(moment.utc(metric.date).date()).padStart(2, "0");

      const existingDayIndex = chartData.findIndex(
        (dataItem) => dataItem.day === day,
      );

      if (existingDayIndex > -1) {
        chartData[existingDayIndex] = {
          ...chartData[existingDayIndex],
          ...metrics[existingDayIndex],
        } as ChartData;
      }
    }

    return { chartData };
  }, [getLogMetricsState, selectedDate]);

  const chartConfig = useMemo(() => {
    return {
      labels: chartData.map((data) => data.day),
      datasets: chartData.map(() => ({
        backgroundColor: computedTailwindConfig.theme.colors.emerald[600],
        data: chartData.map((data) =>
          systemOfRecord?.kind === "bigQuery"
            ? Number(data["processedBytesCount"])
            : Number(data["processedSecondsCount"]),
        ),
      })),
    };
  }, [chartData, systemOfRecord?.kind]);

  const chartOptions = React.useMemo<ChartOptions>(() => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      hoverBackgroundColor:
        chartColor !== undefined ? chartColor.hoverBackgroundColor : undefined,
      hover: {
        mode: "index",
        intersect: false,
      },
      scales: {
        x: {
          stacked: true,
          grid: { display: false },
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
          },
        },
        y: {
          beginAtZero: true,
          ticks: {
            color: chartColor !== undefined ? chartColor.ticks : undefined,
            font: {
              weight: 500,
              lineHeight: 1.25,
            },
            callback: (value) => {
              return value.toLocaleString().replace(/,/g, " ");
            },
          },
          grid: {
            color: chartColor !== undefined ? chartColor.grid : undefined,
          },
          border: {
            display: false,
          },
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: false,
        },
      },
    };
  }, [chartColor]);

  return (
    <div className="size-full p-7">
      <div className="mb-4 flex flex-row items-stretch justify-between">
        <div className="min-w-0 flex-1">
          <div className="font-medium">{formattedMetricText}</div>
          <div className="text-neutral-500 dark:text-neutral-400">
            {systemOfRecord !== undefined && systemOfRecord.kind === "bigQuery"
              ? "processed by Cargo in BigQuery"
              : "computed by Cargo in Snowflake"}
          </div>
        </div>
        <div className="shrink-0">
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.1 }}
            >
              <MonthPicker onChange={setSelectedDate} />
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
      {getLogMetricsState.isLoading === true ? (
        <Spin />
      ) : (
        <div className="h-50 w-full">
          <Chart
            id="sor-metrics"
            type="bar"
            options={chartOptions}
            data={chartConfig}
          />
        </div>
      )}
    </div>
  );
};
