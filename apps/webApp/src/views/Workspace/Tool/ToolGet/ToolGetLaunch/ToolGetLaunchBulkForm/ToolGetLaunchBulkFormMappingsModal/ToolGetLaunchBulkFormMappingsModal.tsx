import ArrowRight from "@untitled-ui/icons-react/build/esm/ArrowRight";
import type { Orchestration } from "cargo-api";
import { Alert } from "cargo-components/Alert";
import { Button } from "cargo-components/Button";
import type { JsonSchemaFormUiSchema } from "cargo-components/JsonSchemaForm/types";
import { Modal } from "cargo-components/Modal";
import type { JsonSchema } from "cargo-utils";
import { fromEntries, unique } from "cargo-utils";
import { Form, useForm } from "components/Form";
import { FileColumnsAutocompleteWidget } from "components/Form/widgets/FileColumnsAutocompleteWidget";
import React from "react";

type Mappings = Record<string, string>;

type Props = {
  s3Filename: string;
  deployedRelease: Orchestration.Release;
  confirmErrorMessage?: string;
  isOpen: boolean;
  isConfirming: boolean;
  onConfirm: (mappings: Mappings) => void;
  onCancel: () => void;
};

export const ToolGetLaunchBulkFormMappingsModal: React.FC<Props> = (props) => {
  const {
    s3Filename,
    deployedRelease,
    confirmErrorMessage,
    isOpen,
    isConfirming,
    onConfirm,
    onCancel,
  } = props;

  const form = useForm<Record<string, string>>({
    initialData: {},
  });

  const jsonSchema = React.useMemo<JsonSchema>(() => {
    if (deployedRelease.formFields === undefined) {
      return {};
    }

    return {
      type: "object",
      properties: fromEntries(
        deployedRelease.formFields.map<[string, JsonSchema]>((formField) => {
          return [formField.slug, { type: "string", title: formField.name }];
        }),
      ),
      required: unique(
        deployedRelease.formFields
          .filter((formField) => {
            return formField.isRequired === true;
          })
          .map((formField) => {
            return formField.slug;
          }),
      ),
    };
  }, [deployedRelease.formFields]);

  const uiSchema = React.useMemo<JsonSchemaFormUiSchema>(() => {
    if (deployedRelease.formFields === undefined) {
      return {};
    }

    return fromEntries(
      deployedRelease.formFields.map<[string, JsonSchemaFormUiSchema]>(
        (formField) => {
          return [
            formField.slug,
            {
              "ui:widget": "FileColumnsAutocompleteWidget",
              "ui:options": {
                s3Filename,
              },
            },
          ];
        },
      ),
    );
  }, [deployedRelease.formFields, s3Filename]);

  const widgets = React.useMemo(() => {
    return {
      FileColumnsAutocompleteWidget,
    };
  }, []);

  return (
    <Modal
      className="w-[500px]"
      title="Review file mappings"
      header={
        <Button
          icon={<ArrowRight />}
          iconPosition="right"
          text="Continue"
          type="primary"
          isDisabled={form.state.isValid === false}
          isLoading={isConfirming === true}
          onClick={() => {
            const mappings = Object.entries(form.state.data).reduce<Mappings>(
              (currentMappings, mappingObject) => {
                const formFieldSlug = mappingObject[0];
                const columnName = mappingObject[1];

                if (columnName === undefined) {
                  return currentMappings;
                }

                currentMappings[columnName] = formFieldSlug;

                return currentMappings;
              },
              {},
            );

            onConfirm(mappings);
          }}
        />
      }
      isOpen={isOpen}
      onCancel={onCancel}
    >
      {confirmErrorMessage !== undefined ? (
        <Alert className="mb-1 w-full" type="error">
          {confirmErrorMessage}
        </Alert>
      ) : null}

      <Form
        className="h-full w-full overflow-y-scroll px-2 pb-4 pt-2"
        id={form.id}
        data={form.state.data}
        jsonSchema={jsonSchema}
        uiSchema={uiSchema}
        widgets={widgets}
        onChange={form.state.setData}
        onIsValidChange={form.state.setIsValid}
      />
    </Modal>
  );
};
