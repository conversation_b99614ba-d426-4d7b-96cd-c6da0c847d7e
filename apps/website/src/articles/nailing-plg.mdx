export const metadata = {
  title: "How to nail your PLG motion in 2025",
  slug: "nailing-plg",
  description:
    "PLG is back in 2025, but how do you get it right. Learn from the experience of Descript, W&B and a host of Cargo's PLG users.",
  authorName: "<PERSON>",
  authorPictureUrl: "/blog/authors/maxence.jpg",
  publishedAt: "2025-04-30T00:00:00Z",
  updatedAt: "2025-04-30T00:00:00Z",
  thumbnailUrl: "/blog/articles/nailing-plg/thumbnail.png",
  tag: "Product-Led Growth",
  readingTimeInMinutes: "5min",
};

Product-led sales is resurging. It’s a time for builders.

<br />

Outbound is in freefall. Buyers ignore mass outreach. “Spray and pray” is dead, and flooding more domains/emails doesn’t work.

<br />

PLG/PLS winners?  
They’ve engineered zero-friction: buyers self-serve, see value, and pay only for what works.

<br />

That’s why Lovable, Augmentcode, Descript, and Replit are scaling faster than anyone.

<br />

But most teams are leaking pipeline everywhere:

<br />

- **Junk signups, zero prioritization, reps still chasing (or ignoring) Gmail addresses.**
- **Data scattered across tools:** Product, enrichment, sales all siloed. No single customer view.
- **CRMs full of noise:** Sales can’t focus; pipeline reviews are chaos.

<br />

**Want to win? Build your GTM like a machine:**  
Unify your data. Automate enrichment and scoring upstream. Only send prioritized, context-rich leads to sales.

<br />

Let’s dive in

<br />

# Common Pitfalls (and How to Fix Them)

<br />

## 1. Your CRM is Not Your System of Record

<br />

> The source of truth isn’t your CRM. It’s your data warehouse.<br />
> The CRM should be a curated shortlist of leads worth a rep’s time.

<br />

Stop treating Salesforce or HubSpot as the mothership. PLG brings noise: bots, junk, and signups at a scale no CRM was built to handle.

<br />

Here’s the truth:

<br />

- Every time you dump raw signups into your CRM, you’re poisoning the well.
- Reps end up chasing junk. High-value accounts get lost in the mess.
- Gmail signups break lead-to-account matching.
- CRM bloat kills sales productivity and turns pipeline reviews into noise.

<Callout
  text={
    <>
      Operator move:
      <br />
      Use your warehouse (Snowflake, BigQuery, etc.) as the source of truth.
      Orchestrate all revenue ops: usage, enrichment, signals, prioritization,
      and lead routing there.
    </>
  }
  className="my-5"
/>{" "}

The CRM is just the handoff: only matched, high-intent, ICP-fit leads should ever reach it.

<br />

> “CRM is not the brain, it’s the handoff. Scoring, routing, and intelligence all start upstream.”  
> — Nicolas Druelle, CEO Revenue Architect

When reps log in, they should see ten ICP warm accounts. Nothing else.

<br />
<br />

## 2. Too Many Signups, Zero Prioritization

<br />

> “Everyone is a lead, so no one is.”

<br />

PLG drives volume. That’s the upside and the problem. Without filtering, a bot, a student, and a Fortune 500 exec all look the same.

<br />

Research shows sales-assisted PLG triples conversion. But reps are expensive and limited, and most leads aren’t ready.

<br />

![sales assisted](/blog/articles/nailing-plg/PLS.png)

<br />

That’s where upstream scoring comes in. Prioritize based on intent, fit, and behavior, _before_ sales ever gets involved.

<br />

<Callout
  text={
    <>
      Operator tip:
      <br />
      High volume of signups? Only surface users who hit a product milestone
      (PQL)—never just email submit. Lower volume? Fit-score every lead to keep
      only ICP. Everything else: automate, nurture, or hand to marketing or to
      automated outreach
    </>
  }
  className="my-5"
/>

## 3. Data & Signals Are Split Across Tools

<br />

Product usage in Amplitude. Firmographics in ZoomInfo. Sales activity in Outreach. CRM in Salesforce.

<br />

Modern stacks are fragmented by default.

<br />

Without unified orchestration, data stays scattered, signals go cold, and buyers fall through the cracks.

<br />

- The same user looks anonymous in one tool, high-intent in another, and never surface as a real opportunity.
- Reps burn hours stitching a journey together, if they bother at all.

<br />

When your stack can’t sense and act on key moments, you can’t grow efficiently.

<br />

> Acquisition dominates initial growth. But at ~$10M ARR, retention becomes the main lever. Before $100M, expansion takes over as the biggest driver. Growth becomes a trifecta: acquisition, retention, and expansion.
>
> <br /> — Jacco Van Der Kooij, Revenue Architecture

<br />

If you don’t have a unified orchestrator, you _can’t_ operationalize the full funnel _(=bowtie)._

- **Expansion triggers** stay buried.
- **Churn risk** goes undetected.
- **Renewals** become reactive firefights.

<br />
<br />

# The Product-Led Revenue Engine (Built Right)

<br />

> Think of your GTM engine like a factory: signals come in, get refined by automation, and only qualified leads get passed to reps.

<br />

## Step 1: Filter Noise at the Gate

<br />

Most teams let junk pollute their pipeline. Operators filter at the source.

<br />

[Up to 50% of free trial signups may be fake accounts](https://www.growthunhinged.com/p/stop-fake-accounts?utm_source=chatgpt.com).  
They pollute metrics, waste enrichment credits, and distract sales.

<br />

Block junk and personal emails _before_ they hit downstream systems. At Descript, the team’s first Cargo Play was an AI classifier and blocklist of fraudulent domains. It filtered thousands of fake signups instantly.

<br />

- The very first action is to maintain a constantly updated blocklist of disposable, temporary, and fraudulent email domains, catching thousands of junk signups instantly.
- The second action is to evaluate whether a signup was coming from a personal or work email, triggering unique and optimized waterfall enrichment for each type.

<br />

## Step 2: Classify and Enrich Every Lead

<br />

![Personal email Enrichment](/blog/articles/nailing-plg/perso_email.gif)

<br />

Every signup starts with a simple question: **Is this a work email, or personal?**

<br />

- **Personal email?** Route to enrichment tools that can map Gmail/Outlook to LinkedIn. If the match is ICP, proceed. Otherwise, discard.
- **Work email?** Run a separate enrichment logic tuned to that segment/email type.

<br />

**Waterfall enrichment is the new standard.**  
One provider doesn’t cut it. Chain vendors, validate matches, and tune the logic for your mix.

<br />

<Callout
  text={
    <>
      Operator tip:
      <br />
      Waterfall enrichment always trades off reach vs. accuracy. Bad data is
      worse than no data. When you chain enrichment vendors, don’t just take the
      first “match”. You need to prioritize by accuracy.
      <br />
      1. If you own the ground truth, battle-test every provider for coverage
      and accuracy.
      <br />
      2. Ask an LLM to validate—Example: “Does this LinkedIn profile really
      match the original signup? Does the job title, geography, and domain fit?”
      <br />
      Only pass a lead downstream if confidence is high.
    </>
  }
  className="my-5"
/>

At Descript, custom waterfalls led to 80% enrichment coverage and 2x pipeline from PLG signups.

<br />

<Callout
  text={
    <>
      Operator tip:
      <br />
      Don’t ignore personal emails. Too many teams block them or treat them as
      low intent by default, cutting out a massive chunk of real buyers. We’ve
      seen enterprise VPs, perfect ICP, using a Gmail just to test-drive the
      product before raising their hand. Action: For every personal email,
      always run LinkedIn and enrichment. If you catch a senior exec from a top
      logo, route to high-touch. Don’t miss your next six-figure deal because
      you filtered by domain.
    </>
  }
  className="my-5"
/>{" "}

<br />

## Step 3: Score Before You Sync

<br />

![PQA/PQL flow](/blog/articles/nailing-plg/PLG_flow_PQA.jpg)

<br />

The only way to keep reps focused (and pipeline healthy) is to _ruthlessly_ filter and score every lead before it gets to sales.

<br />

Your PQL isn't just a usage metric. It's a combination of:

<br />

- Company fit
- User behavior
- Buyer role (optional)

<br />

Only PQLs should reach the CRM and be allocated to reps. Everyone else should be pushed for automated nurture.

<br />

<Callout
  text={
    <>
      Operator tip:
      <br />
      1. Build a list of targeted accounts (Tiers 1) and store it. Anytime
      someone from a _dream account_ signs up, route that lead directly to your
      best AE for white-glove treatment.
      <br />
      2. As soon as an account is PQL, automatically find other stakeholders so
      your reps can easily triangulate within the accounts.
    </>
  }
  className="my-5"
/>

Once your leads are filtered and scored, the next step is deciding how to act on them.

<br />

## Step 4: Allocate the right effort for each lead tier

<br />

![](/blog/articles/nailing-plg/allocation-light.png)

<br />

The best GTM systems don't just identify good leads, they decide what to do with them. Today more than ever, it’s all about maximizing engagement while allocating the right resources based on revenue potential and maturity.

<br />

At Descript, the team designed a system that tailors effort based on lead tier.

<br />

- **Top-tier (high-scoring) leads** go straight to a rep, bundled with everything they need: account research, user behavior summary, and recommended outreach angle, so they can act instantly.
- **Mid- and low-intent leads** get routed through automated nurture tracks. Product usage data (piped through Cargo) feeds into Octave and Instantly, triggering personalized automated sequences. Automated, but relevant.

<br />

This _hybrid model_ means every lead gets the right touch, AEs focus only where it counts, and nothing falls through the cracks.

<br />

See an extract below from our talk with [**G Cabane on how Ramp thinks about this**](https://youtu.be/tOgx9CJgUyU?feature=shared).

<br />

![](/blog/articles/nailing-plg/cabane-ref.png)

<br />

This hybrid logic—AI agents handling the volume, humans focusing on what matters—will define the next era of GTM in the coming months.

<br />

Your edge isn’t volume. It’s precision. Know where to put humans in the loop.

<br />

## Step 5: Enable your sales team with the right information

<br />

> Workflows without enablement is just plumbing.

<br />

**Give reps instant, actionable context:**  
Before every meeting, your AE should have a single snapshot: key product actions, recent engagement, enrichment highlights, and relevant stories, all in one place, zero digging. If reps are clicking through Salesforce to piece this together, you’re losing valuable time that should be spent on selling.

<br />

Example: Trigger an account research each time a new account has been assigned to a rep, so the owner has the entire _(ie: product, CRM, third party)_ context on the lead without clicking everything in SFDC.

<br />

<Callout
  text={
    <>
      Operator tip:
      <br />
      The best teams deliver context and ammo: surfacing not just the 'what
      happened', but also the why it matters (proof points, talk tracks,
      segment-specific plays, customer logos to namedrop) and how to relate it
      to the specific prospect.
    </>
  }
  className="my-5"
/>{" "}

Only pass a lead downstream if confidence is high.

<br />
👉 Read more: [**Unleashing Sales Potential: The Synergy of Sales Ops &
Enablement**](https://www.getcargo.io/blog/unleashing-sales-potential-the-synergy-of-sales-ops-enablement)
<br />
<br />

## It’s Not Just About Acquisition: Orchestrate the Whole Bowtie Funnel

<br />

Most revenue teams stop at acquisition, but a unified GTM engine runs across [the bowtie funnel](https://winningbydesign.com/wp-content/uploads/2024/05/The-Bowtie-A-Proposed-Standard.pdf):

<br />

- **Expansion:** Combine billing (Stripe), usage, and support signals to spot upsell moments.
- **Churn:** Tie contract risk to NPS drops, declining usage, and third-party signals all together.
- **Renewals:** Proactively summarize customer journeys and flag risks _before_ the CSM steps in.

<br />

Example: At Veriff, plugging Stripe and product usage data into Cargo, they jumped from **42 upsell opportunities created in one month to 363 the very next month,** a nearly **10x increase,** just by orchestrating their signals and surfacing expansion-ready accounts proactively.

<br />

👉 Watch the clip: See the actual [pipeline numbers jump](https://youtu.be/6unbfdgBCRw?feature=shared&t=347)

<br />
<br />

# Takeaway

<br />

PLG isn’t just a motion. It’s a system.

<br />

> Don’t duct-tape your funnel. Engineer your revenue engine:<br />
>
> - Unify data and signals<br />
> - Automate enrichment _before_ CRM<br />
> - Prioritize and route by fit or behavior<br />
> - Arm reps with actionable context, not noise<br />
> - Let AI agents do the grunt work. Put humans where it counts

<br />

Once you mastered the PLG motion, see the [3 workflows that build atop your PLG motion](https://www.getcargo.ai/blog/outbound-flywheel-plg)

<br />
<br />
