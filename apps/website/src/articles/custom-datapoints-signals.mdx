export const metadata = {
  title: "How to operationalize custom datapoints & signals",
  slug: "custom-datapoints-signals",
  description:
    "Why the next generation of GTM teams are building with custom datapoints and live signals. Real operator playbooks and practical steps to operationalize data at scale.",
  authorName: "<PERSON>",
  authorPictureUrl: "/blog/authors/maxence.jpg",
  publishedAt: "2025-06-30T00:00:00Z",
  updatedAt: "2025-04-30T00:00:00Z",
  thumbnailUrl: "/blog/articles/nailing-plg/thumbnail.png",
  tag: "GTM",
  readingTimeInMinutes: "7min",
};

> Unstructured data is your edge. While many AI use cases will become standard—what wins is being first to get the upside before results normalize. Companies with unique data sets will have outsized wins. Prioritize plays where your data gives you leverage over competitors.  
> <PERSON><PERSON> — SVP Marketing @Hubspot

<br />

In today’s GTM arms race, every team has ZoomInfo or Apollo. So why does everyone’s outreach feel the same? Because the real edge isn’t in tools. To stand out, you need custom, actionable data points—and a way to operationalize them at scale.

<br />

After we posted about our workshop on custom data points, many GTM operators reached out asking not just what to use, but how to put it into practice. Here’s how the 1% actually do it.

<br />

We’ll get into why generic data ONLY is dead, how LLMs unlock custom insights at scale, and the exact systems operators build to turn custom attributes and signals into a repeatable GTM engine.

<br />

Here’s the actual playbook:

<br />

**Extract**: Find the custom datapoints that matter for your business-specific needs.

**Operationalize**: Wire those attributes into scoring, routing, and messaging flows.

**Refresh**: Make sure every play adapts in real-time. Stale data = dead data.

<br />

![framework datapoints](/blog/articles/custom-datapoints-signals/table.png)

<br />

# 1. Why Third-Party Data Falls Short

<br />
Everyone’s been there: you bought a third-party data provider, hoping for an
edge, just to realize a bit later that It’s the same standard-issue lists each
of your competitor is using. While it’s a good starting point, we all know the
limitations:

- **Commoditized**: Your competitors have the same data, removing your edge.
- **Inaccurate/outdated**: Missing or stale information (headcount, revenue, role changes). Ever check a lead on LinkedIn before reaching out, only to see they left six months ago? If you know, you know.
- **Overly broad**: "Software Development" lumps distinct segments together, and legit SaaS companies are often hidden in other categories.

<br />

![Syftdata comparison image: Perplexity vs Apollo on annual revenue coverage](/blog/articles/custom-datapoints-signals/llm_vs_prodivers.png)

<br />

Currently, LLMs outperform most providers in basic company data, especially information that is publicly available. For instance, a recent analysis by Syftdata revealed that Perplexity provided 40% more data on “annual revenue” than Apollo.

<br />

With tools like Perplexity or Cargo Agent, you can extract hyper-specific, structured data at scale to fill the gaps, enrich what’s missing, and customize your dataset for your actual go-to-market needs.  
It’s about adapting the generic dataset baseline to something truly actionable for your company. It’s all about appropriating the dataset.

<br />
<br />

# 2. The LLM Era: Custom Data at Scale

<br />
<br />

LLMs are changing how we source insights.

<br />

LLMs now pull **custom, business-specific data points at scale** from the public web, press releases, job boards, even investor reports. And you can pipe them straight into your CRM.

<br />

![OH: LLms in Cargo](/blog/articles/custom-datapoints-signals/llm_for_gtm_OH.png)
_Source: [YouTube - AI agent live data extraction](https://www.youtube.com/watch?v=7yKvtxjbQ90)_

<br />

**Examples:**

<br />

- **Vanta**: Does this company have SOC2 Type II?
- **Rippling**: Are they hiring people outside of HQ locations?
- **Sweep**: Are they running sustainability initiatives?
- **Yoobic**: How many physical stores does a company have?

<br />

If you’re running campaigns with these, you outperform anyone blasting generic lists. You show up with context that moves the needle.

<br />

<Callout
  text={
    <>
      Operator Tip:
      <br />
      If a specific data seems difficult to retrieve, use an indicator (0-10
      scale) to estimate likelihood and combine it with confidence scoring for
      reliability.
      <br />
      Ex: On a scale from 0-10, how much do you think &#123;&#123; company_name
      &#125;&#125; is hiring remotely? When doing this, it’s very important to
      also ask AI to provide you with a confidence score and explanation.
    </>
  }
  className="my-5"
/>

![GIF LLM in Cargo](/blog/articles/custom-datapoints-signals/gif2.gif)

<br />

The best custom data points come from deep knowledge of your target market.

Ask yourself: _If I had a list of 10 companies, what attribute or signal would make me pick one as a “perfect fit” over the others?_

<br />

If you’re not sure, just ask our new best friend: AI.

> You can use this **[Chat GPT Cargo Agent](https://chatgpt.com/g/g-686152635674819195dfc101c5ba905d-custom-datapoints-signals)**
> Just ask 'Give me custom datapoints & signals for domain.com'

**Examples by vertical:**

<br />

- **Sales engagement tools**: # of reps + Split AE, BDR, GTM stack, outbound/inbound pipeline mix, sales enablement headcount.
- **Fintech**: FX risk (operating in multiple countries), treasury team presence, payment processor.
- **AI dev tools**: Ratio of junior/senior devs, ML hiring, DevOps stack (IDE, CI/CD), growth or degrowth of engineers (because you can have a “do more with less” angle if your solution brings more efficiency).
- **Retail tech**: Physical store count, seasonality, new market entries.

<br />

And so on. If it’s generic, it’s not an edge. If it’s hard to pull, but matters for closing, it _is_.

<br />

Here’s the wild part: less than 1% of teams are operationalizing custom data at scale.

Most teams stop at using this data for micro-campaigns. Top 1% Operators, though, build these custom data & signals into the very foundation of how they sell, segment, and assign.

<br />
<br />

# 3. The Operator Playbook: Turning Custom Data Into GTM Advantage

<br />
<br />

Surfacing custom data is actually quite easy. Most teams use custom data for micro-campaigns, which is good, but **1% operators wire it into their entire GTM engine:**

<br />

- Scoring every account by custom attributes and dynamic signals, not static firmographics.
- Redesigning territories—not by geo or company size, but by deal potential and strategic fit.
- Trigger sequences that speak to a prospect’s real world, not just “Hey, saw you’re in SaaS.”

<br />

Let’s see how to operationalize it.

<br />

### A. Scoring & Segmentation

<br />
Custom attributes aren’t just for outreach—they power dynamic scoring models.
<br />

![custom datapoints Cargo](/blog/articles/custom-datapoints-signals/custom_dp_cargo.png)

<br />

The goal is a _dynamic_ scoring system. As new data comes in—maybe a company just doubled their SDR headcount, or switched to Salesforce—your model automatically recalculates, and those accounts move up (or down) in priority.

<br />

**The key:** This is a _living system_. Every time the data refreshes, so does your focus. No more static territories, no more outdated scoring, no more dead pipeline.

<br />

### B. Tiering & Resource Allocation

<br />

The goal is to allocate the right resources (i.e., human versus automation/AI agent) based on revenue potential. You need to find the right balance between maximizing engagement and ensuring that most shiny accounts receive white-glove, human-led treatment.

<br />

![Tiering_strategy](/blog/articles/custom-datapoints-signals/tiering.png)

<br />

When you deeply understand a prospect’s GTM setup and priorities, you can craft messaging that resonates at a strategic level, even if it’s automated.

<br />

Highly relevant outreach can even outperform signal-based strategies.

<br />
** Examples:**
<br />

1. **Vanta**
   <br />
   Knowing a company’s target market and their SOC 2 status (e.g., Type 1, Type
   2, or not certified) can be the difference between closing or losing a deal.
   If a company is selling to mid-market or enterprise customers but lacks full
   SOC 2 compliance, that gap can kill deals late in the cycle. With this
   datapoint, you can lead with messaging like:

> Selling to companies like Ramp* or Miro* is tough when you're not SOC 2 compliant. There’s nothing more frustrating than getting the team excited, only to get a cold ‘no’ from security at the finish line. Let’s make sure compliance never stalls your next big win.

<br />

Here again, finding the customer logo of a tatget company can easily be done at scale with LLMs.
This is the power of a well-chosen custom datapoint: it lets you speak directly to a pain the company may not even realize is costing them revenue.

<br />

2. **Deel**
   <br />
   It's not enough to know if a company is remote. What matters is **how
   globally distributed they are**, if they have people working outside of HQ
   locations, and whether they have the infrastructure to handle compliance,
   contracts, and payroll across borders.

Three interesting custom data points could be:

- Founded year: Younger companies are more likely to be remote-native; older companies often struggle with global compliance complexity.
- Geographic distribution of employees: If a company has team members across 5+ countries but no legal entities in those regions, it's a perfect Deel fit.
- Employee growth rate: If they are growing fast in those locations, they will need to have a proper solution to maintain the hiring pace.

<br />

This allows for messaging like:

> Hiring in 6+ countries without local entities? Growing fast but stuck dealing with contracts, currencies, or misaligned benefits? We see this all the time—and it’s why companies like X and Y switched to Deel before it slowed them down."

<br />

You’re not just guessing at pain points. You’re calling out the exact friction in their world, and showing how others solved it.  
That’s what resonates and moves deals forward.

<br />

Some teams are going even further, building their entire book of business and territories around these custom attributes.

<br />
<br />
# 4. Real Example: Modern Territory Planning
<br />
<br />

A leading retail tech company (let’s call them RetailCo) threw out the old playbook of assigning patches by region or employee count.

<br />

They started from defining and extracting their SAM (service addressable market), and finetuned it with specific attributes like:

- Number of physical stores
- Revenue seasonality (is the business spiky or steady?)
- Store format (owned vs. franchised)
- Headcount growth

And built a dynamic “account score” that was refreshed every month.

<br />
<br />

![Tiering_strategy](/blog/articles/custom-datapoints-signals/bob.png)

<br />
<br />

**Result:**

<br />

- Senior reps got assigned “theme” territories (ex: “fashion chains, $500M+ revenue, high seasonality”)
- Lower-fit accounts were pushed to automated marketing/sales sequence, or junior reps.
- Reps stopped wasting cycles on dead accounts, and saw 2x higher conversion rates in priority segments

<Callout
  text={
    <>
      Operator tip:
      <br />
      When a new account signal comes in (e.g., company opens 100 new stores),
      it instantly moves up the list (prioritize the account). Your team is
      first to know and first to act.
    </>
  }
  className="my-5"
/>

<br />

# 5. Conclusion

<br />
Custom data lets you understand your market in ways generic third-party lists
never could. With LLMs, you generate unique datasets and build smarter tiering,
making your GTM radically more efficient.
<br />

The real winners will be the teams that don’t just collect custom data but actually use it to drive smarter orchestration.

<br />

If you want to get out of the noise and actually win, build your own custom data engine. That’s exactly what Cargo is built for.
