"use client";

import { motion } from "framer-motion";
import React from "react";

import { Description } from "@/components/Description";
import { Picture } from "@/components/Picture";
import { Title } from "@/components/Title";

import { HomeHeroAiPrompt } from "./HomeHeroAiPrompt";
import { HomeHeroAnimatedText } from "./HomeHeroAnimatedText";

export const HomeHero: React.FC = () => {
  return (
    <React.Fragment>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          duration: 0.4,
          delay: 0.2,
          type: "spring",
          stiffness: 250,
          damping: 30,
        }}
        className="absolute size-full"
      >
        <Picture
          src="/static/img/pictures/dot-background.png"
          alt="dot-background"
          className="absolute left-1/2 top-1/2 size-full -translate-x-1/2 -translate-y-[51%] scale-110 object-cover"
        />
      </motion.div>
      <div className="relative flex flex-col items-stretch justify-center">
        <div className="relative flex flex-1 items-center justify-center gap-10">
          <div className="flex flex-col items-center justify-center">
            <Title className="relative flex w-screen flex-col items-center justify-center text-center !text-4xl !leading-[3rem] md:w-131 xl:!text-[3.125rem] xl:!leading-[3.875rem]">
              <span>AI Agents for GTM</span>
              <p className="inline-flex gap-2 overflow-hidden whitespace-nowrap text-center">
                <span>Built to</span>
                <HomeHeroAnimatedText />
              </p>
            </Title>
            <HomeHeroAiPrompt />
            <Description className="mt-6 flex flex-col text-balance text-center xl:mt-16">
              <span>
                Manage an AI Workforce that qualify leads, engage prospects,
              </span>{" "}
              <span>
                and convert opportunities—scaling your GTM team without adding
                headcount.
              </span>
            </Description>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};
