"use client";

import Edit04 from "@untitled-ui/icons-react/build/esm/Edit04";
import Shuffle01 from "@untitled-ui/icons-react/build/esm/Shuffle01";
import { Button } from "cargo-components/Button";
import classNames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";

import { Link } from "@/components/Link";
import { config } from "@/config";

import { HomeHeroAiPromptElement } from "./HomeheroAiPromptElement";
import type { HomeHeroAiPromptElementVariant } from "./types";
import { generatePromptBlocks, parsePromptText, prompts } from "./utils";

type PromptAction = "suggestions" | "write";

const suggestionsCount = prompts.length;

export const HomeHeroAiPrompt = React.memo(() => {
  const [promptAction, setPromptAction] =
    React.useState<PromptAction>("suggestions");
  const [promptIndex, setPromptIndex] = React.useState<number>(0);

  const promptElementRefs = React.useRef<(HTMLSpanElement | null)[]>([]);
  const writePromptRef = React.useRef<HTMLTextAreaElement | null>(null);
  const isFirstMount = React.useRef<boolean>(true);

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      if (writePromptRef.current === null) {
        return;
      }

      if (promptAction === "write") {
        writePromptRef.current.focus();
      }
    }, 1000);

    return () => {
      clearTimeout(timeout);
    };
  }, [promptAction]);

  React.useEffect(() => {
    if (isFirstMount.current === true) {
      isFirstMount.current = false;
      return;
    }
  }, [promptAction]);

  const getSuggestions = React.useCallback(() => {
    if (promptAction === "write") {
      return;
    }

    setPromptIndex((prevState) => {
      return (prevState + 1) % suggestionsCount;
    });
    isFirstMount.current = true;
  }, [promptAction]);

  const prompt = React.useMemo(() => {
    return prompts[promptIndex];
  }, [promptIndex]);

  const blocks = React.useMemo(() => {
    return prompt !== undefined
      ? generatePromptBlocks({
          text: prompt.text,
          suggestions: prompt.suggestions,
        })
      : [];
  }, [prompt]);

  const interactiveBlockIndexes = React.useMemo(
    () =>
      blocks
        .map((block, index) => (block.type === "interactive" ? index : -1))
        .filter((index) => index !== -1),
    [blocks],
  );

  const focusNext = React.useCallback(
    (index: number) => {
      const currentInteractiveIndex = interactiveBlockIndexes.indexOf(index);
      const nextInteractiveIndex =
        (currentInteractiveIndex + 1) % interactiveBlockIndexes.length;
      const nextBlockIndex = interactiveBlockIndexes[nextInteractiveIndex];
      if (
        nextBlockIndex !== undefined &&
        promptElementRefs.current[nextBlockIndex]
      ) {
        promptElementRefs.current[nextBlockIndex]?.focus();
      }
    },
    [interactiveBlockIndexes],
  );

  return (
    <motion.div
      initial={{ y: 30, opacity: 0, scale: 0.95 }}
      animate={{ y: 0, opacity: 1, scale: 1 }}
      transition={{
        duration: 0.4,
        delay: 0.2,
        type: "spring",
        stiffness: 250,
        damping: 30,
      }}
      className="mt-18 w-[90vw] rounded-[1.25rem] bg-gradient-to-b from-neutral-800/80 to-neutral-800 p-2 md:w-160 xl:mt-16 xl:w-[50rem]"
    >
      <div className="flex size-full flex-col whitespace-normal break-words rounded-xl border border-neutral-700/60 bg-neutral-925 p-6 text-lg leading-9 md:text-2xl md:text-[1.75rem] md:leading-[3rem] xl:p-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={promptAction}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -15 }}
            transition={{
              duration: 0.1,
              type: "spring",
              stiffness: 250,
              damping: 30,
            }}
            className={classNames(
              "min-h-60 flex-1 md:min-h-24",
              promptAction === "write"
                ? "max-md:flex max-md:flex-col"
                : undefined,
            )}
          >
            {promptAction === "suggestions" && blocks.length > 0 ? (
              <React.Fragment>
                {blocks.map((block, index) => {
                  return block.type === "static" ? (
                    <React.Fragment key={index}>
                      {parsePromptText(block.content).map((part, index) =>
                        part.type === "text" ? (
                          <span key={index} className="text-neutral-400">
                            {part.content}
                          </span>
                        ) : (
                          <br key={index} className={part.className} />
                        ),
                      )}
                    </React.Fragment>
                  ) : (
                    <React.Fragment
                      key={`${prompts?.[promptIndex]?.key}-${block.key}`}
                    >
                      {" "}
                      <HomeHeroAiPromptElement
                        variant={
                          block.variant as HomeHeroAiPromptElementVariant
                        }
                        highlightDelay={
                          isFirstMount.current === true
                            ? 600 + 200 * index
                            : undefined
                        }
                        defaultText={block.defaultText}
                        placeholder={block.placeholder}
                        cta={block.cta}
                        suggestions={block.choices}
                        onChange={() => {}}
                        ref={(element) => {
                          promptElementRefs.current[index] = element;
                        }}
                        onKeyDown={() => {
                          focusNext(index);
                        }}
                      />{" "}
                    </React.Fragment>
                  );
                })}
              </React.Fragment>
            ) : null}
            {promptAction === "write" ? (
              <textarea
                ref={writePromptRef}
                name="promptWrite"
                className="size-full flex-1 resize-none appearance-none border-none bg-transparent p-0 text-lg text-neutral-200 placeholder-neutral-400 focus:ring-0 max-md:min-h-full md:text-xl"
                placeholder="Tell us about your GTM need, and we'll build the roadmap for you"
              />
            ) : null}
          </motion.div>
        </AnimatePresence>

        <div className="flex flex-col items-center justify-between max-md:gap-3 md:mt-18 md:flex-row">
          <div className="flex flex-row items-center justify-start gap-3">
            <Button
              type="secondary"
              className="!h-10 !text-xs md:!text-base"
              onClick={() => {
                setPromptAction("suggestions");
                getSuggestions();
              }}
              icon={<Shuffle01 className="size-4" />}
              iconPosition="left"
              text="Get suggestions"
            />
            <div className="flex flex-row items-center justify-start gap-3">
              <Button
                type="secondary"
                className="!h-10 !text-xs md:!text-base"
                onClick={() => {
                  setPromptAction("write");
                }}
                icon={<Edit04 className="size-4" />}
                iconPosition="left"
                text="Write a prompt"
              />
            </div>
          </div>
          <div className="flex flex-row items-center justify-center gap-3 max-md:w-full md:justify-start">
            <Link
              href={config.links.appUrl}
              target="_blank"
              size="large"
              className="px-4 text-neutral-100"
            >
              <span className="text-xs font-semibold md:text-base">
                Get started for free
              </span>
            </Link>
          </div>
        </div>
      </div>
    </motion.div>
  );
});

HomeHeroAiPrompt.displayName = "HomeHeroAiPrompt";
