import type { PromptBlock } from "./types";

export const prompts = [
  {
    key: "accountResearcher",
    text: `{You’re a top-1% account researcher. Identify and enrich the key stakeholders at} [first] {prioritize} <br className="max-md:hidden" /> [second]`,
    suggestions: {
      first: {
        defaultText: "ramp.com",
        placeholder: "Website",
        cta: "Who it's for",
        variant: "yellow",
        choices: ["ramp.com", "deel.com", "figma.com", "miro.com"],
      },
      second: {
        defaultText: "data engineers",
        cta: "Who it's for",
        placeholder: "role",
        variant: "violet",
        choices: ["data engineers", "legal team", "product team"],
      },
    },
  },
  {
    key: "enrichmentOperator",
    text: "{You’re a data enrichment operator. Surface the} [first] {for} [second]",
    suggestions: {
      first: {
        defaultText: "email",
        placeholder: "action",
        cta: "Which action",
        variant: "yellow",
        choices: ["email", "phone number", "linkedin"],
      },
      second: {
        defaultText: "<PERSON><PERSON>",
        cta: "Who it's for",
        placeholder: "name",
        variant: "violet",
        choices: ["<PERSON><PERSON>", "<PERSON>", "<PERSON> <PERSON>man"],
      },
    },
  },
  {
    key: "gtmStrategist",
    text: "{You’re an elite GTM strategist. Build a concise account plan for} [first] {—include} [second] {and actionable angles for outreach. Prioritize insights that will help our reps break in.}",
    suggestions: {
      first: {
        defaultText: "rippling.com",
        placeholder: "Website",
        cta: "Who it's for",
        variant: "yellow",
        choices: [
          "rippling.com",
          "stripe.com",
          "vercel.com",
          "augmentcode.com",
        ],
      },
      second: {
        defaultText: "Buying signal",
        placeholder: "Trigger",
        cta: "What to look for",
        variant: "violet",
        choices: [
          "Buying signal",
          "Current pain point",
          "Tech stack",
          "Buying committee",
        ],
      },
    },
  },
  {
    key: "gtmAnalyst",
    text: "{You’re a GTM analyst. Review} [first] {profile and score their fit for our solution as strong, moderate, or weak.} [second] {with 2–3 specific signals.}",
    suggestions: {
      first: {
        defaultText: "airbyte.com",
        placeholder: "Website",
        cta: "Who it's for",
        variant: "yellow",
        choices: ["airbyte.com", "algolia.com", "reddit.com", "descript.com"],
      },
      second: {
        defaultText: "Provide the best next action",
        cta: "What to do next",
        placeholder: "Justify your reasoning",
        variant: "violet",
        choices: [
          "Provide the best next action",
          "Draft intro email ",
          "Namedrop the best 3 logos",
        ],
      },
    },
  },
];

export function replaceOccurences(
  text: string,
  replacements: Record<string, string>,
): string {
  return text.replace(/\[([^\]]+)]/g, (_, key) => {
    return Object.prototype.hasOwnProperty.call(replacements, key)
      ? (replacements[key] ?? key)
      : key;
  });
}

export function parsePromptText(text: string) {
  const regex = /(.*?)(<br(?:\s+className=['"]([^'"]+)['"])?\s*\/?>|$)/gs;

  const result: {
    type: "text" | "br";
    content?: string;
    className?: string;
  }[] = [];

  for (const match of text.matchAll(regex)) {
    const textPart = match[1];
    const brTag = match[2];
    const className = match[3];

    if (textPart !== undefined && textPart.length > 0) {
      result.push({ type: "text", content: textPart });
    }

    if (brTag !== undefined && brTag.startsWith("<br")) {
      result.push({ type: "br", ...(className ? { className } : {}) });
    }
  }
  return result;
}

export function generatePromptBlocks(prompt: {
  text: string;
  suggestions: Record<
    string,
    {
      defaultText: string;
      placeholder: string;
      cta: string;
      variant: string;
      choices: string[];
    }
  >;
}): PromptBlock[] {
  const { text, suggestions } = prompt;
  const result: PromptBlock[] = [];
  const regex = /(\{[^}]+\}|\[[^\]]+\])/g;
  let lastIndex = 0;
  let match: RegExpExecArray | null;

  while ((match = regex.exec(text))) {
    if (match.index > lastIndex) {
      const staticText = text.slice(lastIndex, match.index).trim();
      if (staticText.length > 0) {
        result.push({ type: "static", content: staticText });
      }
    }
    if (match[0].startsWith("{")) {
      result.push({
        type: "static",
        content: match[0].slice(1, -1).trim(),
      });
    } else if (match[0].startsWith("[")) {
      const key = match[0].slice(1, -1).trim();
      const suggestionEntry = suggestions[key];
      if (suggestionEntry) {
        result.push({
          type: "interactive",
          key,
          ...suggestionEntry,
        });
      }
    }
    lastIndex = match.index + match[0].length;
  }
  if (lastIndex < text.length) {
    const staticText = text.slice(lastIndex).trim();
    if (staticText) {
      result.push({ type: "static", content: staticText });
    }
  }
  return result;
}
