import Lightbulb05 from "@untitled-ui/icons-react/build/esm/Lightbulb05";
import classNames from "classnames";

type CalloutProps = {
  text: React.ReactNode;
  className?: string;
};

export const Callout: React.FC<CalloutProps> = ({ text, className }) => (
  <div
    role="note"
    className={classNames(
      "flex items-center justify-start gap-2 rounded-xl border border-neutral-850 bg-neutral-925 p-2 md:gap-3 md:p-3",
      className,
    )}
  >
    <Lightbulb05 className="size-5 shrink-0 self-start text-yellow-400 svg-path:stroke-1 md:size-7" />
    <div className="flex-1 break-words text-sm font-normal text-neutral-100 md:leading-6">
      {text}
    </div>
  </div>
);
