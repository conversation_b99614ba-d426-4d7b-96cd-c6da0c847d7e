import Download02 from "@untitled-ui/icons-react/build/esm/Download02";
import { useClickOutside, useEventListener } from "cargo-react-utils";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";

import Cargo<PERSON>ogo from "/public/static/img/logos/logo.svg";
import CargoLogoSimple from "/public/static/img/logos/logo-simple.svg";
import { DemoButton } from "@/components/DemoButton";
import { Link } from "@/components/Link";
import { config } from "@/config";

import { HeaderNavigationDesktopMenuProduct } from "./HeaderNavigationDesktopMenuProduct";
import { HeaderNavigationDesktopMenuStories } from "./HeaderNavigationDesktopMenuStories";
import type { HeaderLink } from "./links";
import { links } from "./links";
import type { MenuDirection } from "./types";
import { getDirection, menuTransition } from "./utils";

export const HeaderNavigationDesktop: React.FC = () => {
  const [showLogoPack, setShowLogoPack] = React.useState<boolean>(false);

  const [menuX, setMenuX] = React.useState<number | null>(null);
  const [hoverMenuId, setHoverMenuId] = React.useState<string | null>(null);
  const [activeMenuId, setActiveMenuId] = React.useState<string | null>(null);

  const logoPackRef = React.useRef<HTMLDivElement | null>(null);
  const previousActiveMenuId = React.useRef<string | null>(null);

  React.useEffect(() => {
    previousActiveMenuId.current = activeMenuId;
  }, [activeMenuId]);

  useClickOutside(logoPackRef, (event) => {
    if (logoPackRef?.current?.contains(event.target as Node) === false) {
      setShowLogoPack(false);
    }
  });

  useEventListener(
    "contextmenu",
    (event) => {
      if (
        logoPackRef.current !== null &&
        logoPackRef.current.contains(event.target as Node)
      ) {
        event.preventDefault();
        setShowLogoPack(true);
      }
    },
    logoPackRef,
  );

  useEventListener(
    "dblclick",
    () => {
      if (logoPackRef.current !== null) {
        setShowLogoPack(true);
      }
    },
    logoPackRef,
  );

  const onMouseEnter =
    (link: HeaderLink) => (event: React.MouseEvent<HTMLDivElement>) => {
      const isLink = "href" in link;

      setHoverMenuId(link.id);

      if (isLink === false) {
        setActiveMenuId(link.id);
        setMenuX(event.currentTarget.offsetLeft);
      } else {
        setActiveMenuId(null);
        setMenuX(null);
      }
    };

  const onMouseLeave: React.MouseEventHandler<HTMLElement> = () => {
    setActiveMenuId(null);
    setHoverMenuId(null);
    previousActiveMenuId.current = null;
  };

  const onReset = () => {
    setActiveMenuId(null);
    setHoverMenuId(null);
    previousActiveMenuId.current = null;
  };

  const direction = React.useMemo(() => {
    return getDirection(activeMenuId, previousActiveMenuId.current);
  }, [activeMenuId]);

  return (
    <div className="relative mx-auto hidden w-full max-w-282 grid-cols-12 items-center justify-center xl:grid">
      <div ref={logoPackRef} className="relative shrink-0 justify-self-start">
        <AnimatePresence>
          {showLogoPack === true ? (
            <motion.div
              className="absolute left-1/2 top-14 flex w-90 flex-col items-center rounded bg-neutral-800 p-8"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              style={{ translateX: "-50%" }}
            >
              <div className="mb-8 flex size-28 items-center justify-center rounded-full bg-neutral-900">
                <CargoLogoSimple className="h-16 w-12 text-neutral-100" />
              </div>
              <span className="mb-2 text-xl font-semibold leading-6 text-neutral-100">
                Looking for our logo?
              </span>
              <span className="mb-6 text-base font-normal leading-6 text-neutral-400">
                You got covered
              </span>
              <Link
                href="/static/img/logo-pack.zip"
                variant="secondary"
                icon={<Download02 className="size-4" />}
              >
                Download logo pack
              </Link>
            </motion.div>
          ) : null}
        </AnimatePresence>
        <Link href="/" variant="neutral" size="auto" className="relative">
          <CargoLogo className="h-7 w-24 text-neutral-100" />
        </Link>
      </div>

      <nav
        onMouseLeave={onMouseLeave}
        className="relative col-span-6 col-start-4 flex w-full flex-row items-center justify-center gap-2"
      >
        {links.map((link, index) => {
          const isActive = hoverMenuId === link.id;
          const isLink = "href" in link;

          return (
            <div
              key={index}
              className="group relative cursor-pointer text-sm font-medium leading-6 text-neutral-400 transition-all duration-300 ease-in-out hover:text-neutral-200"
              onMouseEnter={onMouseEnter(link)}
            >
              <AnimatePresence initial={false}>
                {isActive === true ? (
                  <motion.div
                    layoutId="menu-underline"
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.1 }}
                    className="absolute left-0 top-0 -z-layer-1 size-full rounded-md bg-neutral-800"
                  />
                ) : null}
              </AnimatePresence>
              {isLink === false ? (
                <div className="relative flex items-center justify-center px-2.5 py-1.5">
                  <span>{link.text}</span>
                </div>
              ) : (
                <div className="relative flex items-center justify-center">
                  <Link
                    href={link.href}
                    variant="neutral"
                    size="auto"
                    className="relative px-2.5 py-1.5"
                  >
                    {link.text}
                  </Link>
                </div>
              )}
            </div>
          );
        })}
        <AnimatePresence mode="wait">
          {activeMenuId !== null ? (
            <motion.div
              className="absolute top-2 mt-6"
              style={{
                left: menuX || 0,
              }}
              transition={menuTransition}
            >
              <motion.div
                layoutId="content-menu"
                initial={{
                  opacity: 0,
                  scale: 0.9,
                  y: -5,
                }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  y: 0,
                }}
                exit={{
                  opacity: 0,
                  scale: 0.9,
                  y: -5,
                }}
                transition={menuTransition}
              >
                <div className="mt-3 overflow-hidden rounded-xl border-2 border-neutral-800 bg-neutral-900 p-2">
                  <div className="inline-flex h-auto w-max flex-col items-start justify-start overflow-hidden rounded-md border border-neutral-850 bg-neutral-925">
                    <AnimatePresence custom={direction}>
                      {menuContentMapper[activeMenuId]?.({
                        direction,
                        onReset,
                      })}
                    </AnimatePresence>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          ) : null}
        </AnimatePresence>
      </nav>

      <div className="col-span-3 col-start-10 flex shrink-0 items-stretch justify-end gap-2">
        <DemoButton text="Book a demo" type="secondary" />
        <Link href={config.links.appUrl} variant="primary" className="flex">
          Sign in
        </Link>
      </div>
    </div>
  );
};

type MenuContentMapperRenderParams = {
  direction: MenuDirection;
  onReset: () => void;
};

const menuContentMapper: Record<
  string,
  (params: MenuContentMapperRenderParams) => React.ReactNode
> = {
  product: (params) => (
    <HeaderNavigationDesktopMenuProduct
      direction={params.direction}
      onReset={params.onReset}
    />
  ),
  stories: (params) => (
    <HeaderNavigationDesktopMenuStories
      direction={params.direction}
      onReset={params.onReset}
    />
  ),
};
