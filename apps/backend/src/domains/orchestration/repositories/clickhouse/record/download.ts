import { generateFilename } from "../../../../../utils/file.js";
import type { OrchestrationTypes } from "../../../index.js";
import type { OrchestrationClickhouseRepositoriesDependencies } from "../dependencies.js";
import { getExecutionsFilterClickhouseQuery } from "../utils.js";

export type DownloadPayload = {
  workspaceUuid: string;
  workflowUuid: string;
  ids?: string[];
  title?: string;
  releaseUuid?: string;
  batchUuid?: string;
  statuses?: OrchestrationTypes.RecordStatus[];
  parentBatchUuid?: string;
  parentRunUuid?: string;
  parentNodeUuid?: string;
  isGroupParent?: boolean;
  isFinished?: boolean;
  executionsFilter?: OrchestrationTypes.ExecutionsFilter;
  createdAfter?: Date;
  createdBefore?: Date;
};

export type DownloadOptions = {
  nodeSlugs: string[];
  s3Prefix: string;
};

export const download = async (
  dependencies: OrchestrationClickhouseRepositoriesDependencies,
  payload: DownloadPayload,
  options: DownloadOptions,
): Promise<string> => {
  const { clickhouseClient, logger, config } = dependencies;

  const {
    workspaceUuid,
    workflowUuid,
    ids,
    title,
    batchUuid,
    releaseUuid,
    statuses,
    isFinished,
    parentBatchUuid,
    parentRunUuid,
    parentNodeUuid,
    isGroupParent,
    executionsFilter,
    createdAfter,
    createdBefore,
  } = payload;

  const executionsFilterQuery =
    executionsFilter !== undefined
      ? getExecutionsFilterClickhouseQuery(executionsFilter)
      : undefined;

  const { nodeSlugs, s3Prefix } = options;

  const s3Key = `${s3Prefix}/${generateFilename("csv.gz")}`;

  try {
    const query = `
      insert into function s3(
        'https://${config.aws.s3.bucketName}.s3.amazonaws.com/${s3Key}', 
        '${config.aws.accessKeyId}',
        '${config.aws.secretAccessKey}',
        'CSVWithNames', 
        '_uuid UUID, _workspace_uuid UUID, _workflow_uuid UUID, _record_id String, _record_title String, _created_at DateTime64, _finished_at DateTime64, _status String, _error_message String, ${nodeSlugs.map((nodeSlug) => `${nodeSlug} String`).join(", ")}', 
        'gzip'
      )
      select
        "record"."run_uuid" as "_uuid",
        "record"."workspace_uuid" as "_workspace_uuid",
        "record"."workflow_uuid" as "_workflow_uuid",
        "record"."id" as "_record_id",
        "record"."title" as "_record_title",
        "record"."created_at" as "_created_at",
        "record"."finished_at" as "_finished_at",
        "record"."status" as "_status",
        "record"."error_message" as "_error_message",
        ${nodeSlugs
          .map((nodeSlug) => {
            return `maxIf(execution.title, execution.node_slug = '${nodeSlug}') as "${nodeSlug}"`;
          })
          .join(",")}
      from orchestration.records "record" final
      array join "records"."executions" as "execution"
      where
        "record"."workspace_uuid" = {workspaceUuid: UUID}
        and  "record"."workflow_uuid" = {workflowUuid: UUID}
        ${ids !== undefined ? `and "id" in {ids: Array(String)}` : ""}
        ${parentBatchUuid !== undefined ? `and ("parent_batch_uuid" = {parentBatchUuid: UUID} or "group"."batch_uuid" = {parentBatchUuid: UUID})` : ""}
        ${parentRunUuid !== undefined ? `and ("parent_run_uuid" = {parentRunUuid: UUID} or "group"."run_uuid" = {parentRunUuid: UUID})` : ""}
        ${parentNodeUuid !== undefined ? `and ("parent_node_uuid" = {parentNodeUuid: UUID} or "group"."node_uuid" = {parentNodeUuid: UUID})` : ""}
        ${isGroupParent === true ? `and ("is_group_parent" = true or "group"."batch_uuid" is not null)` : ""}
        ${isGroupParent === false ? `and (("is_group_parent" = false or "is_group_parent" is null) and "group"."batch_uuid" is null)` : ""}
        ${batchUuid !== undefined ? `and  "record"."batch_uuid" = {batchUuid: UUID}` : ""}
        ${releaseUuid !== undefined ? `and  "record"."release_uuid" = {releaseUuid: UUID}` : ""}
        ${statuses !== undefined ? `and  "record"."status" in {statuses: Array(String)}` : ""}
        ${title !== undefined ? `and  "record"."title" ilike {title: String}` : ""}
        ${isFinished === true ? `and  "record"."finished_at" is not null` : ""}
        ${isFinished === false ? `and  "record"."finished_at" is null` : ""}
        ${executionsFilterQuery !== undefined ? `and ${executionsFilterQuery}` : ""}
        ${createdAfter !== undefined ? `and  "record"."created_at" > {createdAfter: DateTime64}` : ""}
        ${
          createdBefore !== undefined
            ? `and  "record"."created_at" <= {createdBefore: DateTime64}`
            : ""
        }
        group by 1, 2, 3, 4, 5, 6, 7, 8, 9
      `;

    await clickhouseClient.query({
      query,
      query_params: {
        workspaceUuid,
        workflowUuid,
        ids,
        title,
        parentBatchUuid,
        parentRunUuid,
        parentNodeUuid,
        statuses,
        batchUuid,
        releaseUuid,
        createdAfter,
        createdBefore,
      },
    });

    return s3Key;
  } catch (error) {
    logger.error(`Failed to list records on Clickhouse`, { payload, error });

    throw error;
  }
};
