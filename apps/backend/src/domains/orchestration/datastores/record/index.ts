import type { OrchestrationTypes } from "../../index.js";
import type { OrchestrationDatastoresDependencies } from "../dependencies.js";
import type { CountPayload } from "./count.js";
import { count } from "./count.js";
import { download, type DownloadPayload } from "./download.js";
import type { GetMetricsPayload } from "./getMetrics.js";
import { getMetrics } from "./getMetrics.js";
import type { ListPayload } from "./list.js";
import { list } from "./list.js";
import type { ListRunsUuidsPayload } from "./listRunsUuids.js";
import { listRunsUuids } from "./listRunsUuids.js";

export type OrchestrationRecordDatastore = {
  list: (payload: ListPayload) => Promise<OrchestrationTypes.Record[]>;
  listRunsUuids: (payload: ListRunsUuidsPayload) => Promise<string[]>;
  count: (payload: CountPayload) => Promise<number>;
  getMetrics: (
    payload: GetMetricsPayload,
  ) => Promise<OrchestrationTypes.RecordMetric[]>;
  download: (payload: DownloadPayload) => Promise<string>;
};

export const buildRecordDatastore = (
  dependencies: OrchestrationDatastoresDependencies,
): OrchestrationRecordDatastore => {
  return {
    list: (payload) => list(dependencies, payload),
    listRunsUuids: (payload) => listRunsUuids(dependencies, payload),
    count: (payload) => count(dependencies, payload),
    getMetrics: (payload) => getMetrics(dependencies, payload),
    download: (payload) => download(dependencies, payload),
  };
};
