import type { OrchestrationTypes } from "../../index.js";
import type { OrchestrationDatastoresDependencies } from "../dependencies.js";

export type DownloadPayload = {
  workspaceUuid: string;
  workflowUuid: string;
  ids?: string[];
  title?: string;
  releaseUuid?: string;
  batchUuid?: string;
  statuses?: OrchestrationTypes.RecordStatus[];
  parentBatchUuid?: string;
  parentRunUuid?: string;
  parentNodeUuid?: string;
  isGroupParent?: boolean;
  isFinished?: boolean;
  executionsFilter?: OrchestrationTypes.ExecutionsFilter;
  createdAfter?: Date;
  createdBefore?: Date;
};

export const download = async (
  dependencies: OrchestrationDatastoresDependencies,
  payload: DownloadPayload,
): Promise<string> => {
  const { repositories, providers } = dependencies;
  const { workspaceUuid, workflowUuid } = payload;

  const workflow = await repositories.pg.workflow.get(
    {
      workspaceUuid,
      uuid: workflowUuid,
    },
    { enrich: false },
  );

  const s3Key = await repositories.clickhouse.record.download(payload, {
    s3Prefix: `downloads/workspaces/${workspaceUuid}`,
    nodeSlugs:
      workflow !== undefined
        ? workflow.nodes
            .filter((node) => node.isArchived === false)
            .map((node) => node.slug)
        : [],
  });

  return providers.s3.getObjectUrl(s3Key);
};
