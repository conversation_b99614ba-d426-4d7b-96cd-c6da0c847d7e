import type { OrchestrationTypes } from "../../index.js";
import type { OrchestrationServicesDependencies } from "../dependencies.js";

export type DownloadPayload = {
  workspaceUuid: string;
  workflowUuid: string;
  ids?: string[];
  title?: string;
  releaseUuid?: string;
  batchUuid?: string;
  statuses?: OrchestrationTypes.RecordStatus[];
  parentBatchUuid?: string;
  parentRunUuid?: string;
  parentNodeUuid?: string;
  isGroupParent?: boolean;
  isFinished?: boolean;
  executionsFilter?: OrchestrationTypes.ExecutionsFilter;
  createdAfter?: Date;
  createdBefore?: Date;
};

export const download = async (
  dependencies: OrchestrationServicesDependencies,
  payload: DownloadPayload,
): Promise<string> => {
  const { datastores } = dependencies;

  return datastores.record.download(payload);
};
