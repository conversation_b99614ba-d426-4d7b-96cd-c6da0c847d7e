import { chunk, fromEntries, getEntries } from "cargo-utils";

import type { S3Client } from "../../../../../../providers/s3/index.js";
import {
  type OrchestrationTypes,
  OrchestrationUtils,
} from "../../../../index.js";
import type { OrchestrationWorkflowActivitiesDependencies } from "../../dependencies.js";
import {
  CHUNK_SIZE,
  type HandleDataCreateRunsPayload,
  type HandleDataOptions,
} from "./handleData.js";

const RUNS_LIMIT = 50_000;

export type HandleRunsDataPayload = {
  workspaceUuid: string;
  workflowUuid: string;
  filter: OrchestrationTypes.BatchDataRunsFilter;
  reset: OrchestrationTypes.BatchDataRunsReset;
  createRuns: (payload: HandleDataCreateRunsPayload) => Promise<void>;
};

export const handleRunsData = async (
  dependencies: OrchestrationWorkflowActivitiesDependencies,
  payload: HandleRunsDataPayload,
  options: HandleDataOptions,
): Promise<void> => {
  const { providers, datastores } = dependencies;
  const { workspaceUuid, workflowUuid, filter, reset, createRuns } = payload;
  const { heartbeat } = options;

  const [release] = await datastores.release.list({
    workspaceUuid,
    workflowUuid,
    statuses: ["deployed"],
    limit: 1,
  });

  if (release === undefined) {
    throw new Error("Unable to retrieve deployed release");
  }

  let offset = 0;

  while (offset % RUNS_LIMIT === 0) {
    let runs: OrchestrationTypes.Run[] = [];

    if (filter.kind === "records") {
      const records = await datastores.record.list({
        workspaceUuid,
        workflowUuid,
        ids: filter.ids,
        title: filter.title,
        releaseUuid: filter.releaseUuid,
        batchUuid: filter.batchUuid,
        statuses: filter.statuses,
        parentBatchUuid: filter.parentBatchUuid,
        parentNodeUuid: filter.parentNodeUuid,
        parentRunUuid: filter.parentRunUuid,
        isGroupParent: filter.isGroupParent,
        executionsFilter: filter.executionsFilter,
        limit: RUNS_LIMIT,
        offset,
      });

      runs = records.map((record) => {
        return {
          ...record,
          uuid: record.runUuid,
          recordId: record.id,
          recordTitle: record.title,
          parentUuid: record.parentRunUuid,
          deletedAt: null,
        };
      });
    } else {
      runs = await datastores.run.list({
        workspaceUuid,
        workflowUuid,
        uuids: filter.uuids,
        batchUuid: filter.batchUuid,
        releaseUuid: filter.releaseUuid,
        statuses: filter.statuses,
        parentBatchUuid: filter.parentBatchUuid,
        parentNodeUuid: filter.parentNodeUuid,
        parentUuid: filter.parentRunUuid,
        isGroupParent: filter.isGroupParent,
        executionsFilter: filter.executionsFilter,
        limit: RUNS_LIMIT,
        offset,
      });
    }

    offset = offset + runs.length;

    const runsChunks = chunk(runs, CHUNK_SIZE);

    for (const runsChunk of runsChunks) {
      if (heartbeat !== undefined) {
        heartbeat();
      }

      const payloads = await Promise.all(
        runsChunk.map(async (run) =>
          getRunHandleDataCreateRunPayload(dependencies, {
            s3Client: providers.s3,
            release,
            run,
            reset,
          }),
        ),
      );

      const payloadExists = (
        payload: HandleDataCreateRunsPayload[number] | undefined,
      ): payload is HandleDataCreateRunsPayload[number] => {
        return payload !== undefined;
      };

      const finalPayloads = payloads.filter(payloadExists);

      await createRuns(finalPayloads);
    }
  }
};

export type HandleDataCreateRunPayload = HandleDataCreateRunsPayload[number];

const getRunHandleDataCreateRunPayload = async (
  dependencies: OrchestrationWorkflowActivitiesDependencies,
  payload: {
    s3Client: S3Client;
    release: OrchestrationTypes.Release;
    run: OrchestrationTypes.Run;
    reset: OrchestrationTypes.BatchDataRunsReset;
  },
): Promise<HandleDataCreateRunPayload | undefined> => {
  const { s3Client, release, run, reset } = payload;

  const nodes = OrchestrationUtils.getReleaseNodes(
    release,
    run.isGroupParent === true && run.parentNodeUuid !== null
      ? run.parentNodeUuid
      : undefined,
  );

  const executions = await getExecutions(dependencies, { nodes, run, reset });

  if (executions === undefined) {
    return undefined;
  }

  const context = await getContext(
    s3Client,
    executions,
    nodes,
    run.contextS3Filename,
  );

  const computedConfigs =
    reset.from === "firstNode"
      ? { start: null }
      : await getComputedConfigs(
          s3Client,
          executions,
          nodes,
          run.computedConfigsS3Filename,
        );

  return {
    recordId: run.recordId,
    recordTitle: run.recordTitle,
    context,
    computedConfigs,
    executions: executions,
    isGroupParent: run.isGroupParent !== null ? run.isGroupParent : undefined,
    parentUuid: run.parentUuid !== null ? run.parentUuid : undefined,
    parentNodeUuid:
      run.parentNodeUuid !== null ? run.parentNodeUuid : undefined,
    parentBatchUuid:
      run.parentBatchUuid !== null ? run.parentBatchUuid : undefined,
  };
};

const getComputedConfigs = async (
  s3Client: S3Client,
  executions: OrchestrationTypes.Execution[],
  nodes: OrchestrationTypes.Node[],
  computedConfigsS3Filename: string,
) => {
  const { body: stringifiedComputedConfigs } = await s3Client.downloadObject(
    computedConfigsS3Filename,
  );
  const computedConfigs = JSON.parse(stringifiedComputedConfigs || "{}");

  const previousNodeUuids = executions
    .slice(0, executions.length - 1)
    .map((execution) => execution.nodeUuid);

  const previousNodes = nodes.filter((node) =>
    previousNodeUuids.includes(node.uuid),
  );

  const previousNodeSlugs = previousNodes.map((node) => node.slug);

  return fromEntries(
    getEntries(computedConfigs).filter(([slug]) =>
      previousNodeSlugs.includes(slug),
    ),
  );
};

const getContext = async (
  s3Client: S3Client,
  executions: OrchestrationTypes.Execution[],
  nodes: OrchestrationTypes.Node[],
  contextS3Filename: string,
): Promise<Record<string, unknown>> => {
  const { body: stringifiedContext } =
    await s3Client.downloadObject(contextS3Filename);
  const context = JSON.parse(stringifiedContext || "{}");

  const previousNodeUuids = executions
    .slice(0, executions.length - 1)
    .map((execution) => execution.nodeUuid);

  const previousNodes = nodes.filter((node) =>
    previousNodeUuids.includes(node.uuid),
  );

  const previousNodeSlugs = previousNodes.map((node) => node.slug);

  return fromEntries(
    getEntries(context).filter(([slug]) => previousNodeSlugs.includes(slug)),
  );
};

const getExecutions = async (
  dependencies: OrchestrationWorkflowActivitiesDependencies,
  payload: {
    nodes: OrchestrationTypes.Node[];
    run: OrchestrationTypes.Run;
    reset: OrchestrationTypes.BatchDataRunsReset;
  },
): Promise<OrchestrationTypes.Execution[] | undefined> => {
  const { nodes, run, reset } = payload;

  if (reset.from === "firstNode") {
    const startNode = OrchestrationUtils.getStartNode(nodes);

    if (startNode === undefined) {
      return undefined;
    }

    const nextNodeUuid = startNode.childrenUuids[0];
    const nextNode =
      nextNodeUuid !== undefined
        ? nodes.find((node) => node.uuid === nextNodeUuid)
        : undefined;

    return OrchestrationUtils.getDefaultExecutions(dependencies, {
      workspaceUuid: run.workspaceUuid,
      recordTitle: run.recordTitle,
      startNode,
      nextNode,
    });
  }

  if (reset.from === "lastNode") {
    const nextExecutionIndex = run.executions.length - 1;

    const previousExecutions = run.executions.slice(0, nextExecutionIndex);
    const nextExecution = run.executions[nextExecutionIndex];

    if (nextExecution === undefined) {
      return previousExecutions;
    }

    const nextNode = nodes.find((node) => node.uuid === nextExecution.nodeUuid);

    if (nextNode === undefined) {
      return previousExecutions;
    }

    return [
      ...previousExecutions,
      await OrchestrationUtils.getExecutionFromNode(dependencies, {
        workspaceUuid: run.workspaceUuid,
        node: nextNode,
        status: "pending",
      }),
    ];
  }

  if (reset.from === "nextNode") {
    const lastExecutionIndex = run.executions.length - 1;
    const lastExecution = run.executions[lastExecutionIndex];

    if (lastExecution === undefined || lastExecution.status === "pending") {
      return undefined;
    }

    const lastNodeUuid = lastExecution.nodeUuid;
    const lastNode = nodes.find((node) => node.uuid === lastNodeUuid);

    if (lastNode === undefined) {
      return undefined;
    }

    const childNodeUuid =
      lastExecution.status === "success" &&
      lastExecution.nodeChildIndex !== null &&
      lastNode.childrenUuids[lastExecution.nodeChildIndex] !== null
        ? lastNode.childrenUuids[lastExecution.nodeChildIndex]
        : undefined;

    const fallbackNodeUuid =
      lastExecution.status === "error" &&
      lastNode.fallbackOnFailure === true &&
      lastNode.fallbackChildUuid !== undefined
        ? lastNode.fallbackChildUuid
        : undefined;

    const nextNodeUuid =
      childNodeUuid !== undefined ? childNodeUuid : fallbackNodeUuid;

    if (nextNodeUuid === undefined) {
      return undefined;
    }

    const nextNode = nodes.find((node) => node.uuid === nextNodeUuid);

    if (nextNode === undefined) {
      return undefined;
    }

    const previousExecutions = run.executions.slice(0, lastExecutionIndex);

    return [
      ...previousExecutions,
      {
        ...lastExecution,
        nextNodeUuid: nextNode.uuid,
      },
      await OrchestrationUtils.getExecutionFromNode(dependencies, {
        workspaceUuid: run.workspaceUuid,
        node: nextNode,
        status: "pending",
      }),
    ];
  }

  if (reset.from === "node") {
    const nextExecutionIndex = run.executions.findIndex((execution) => {
      return execution.nodeUuid === reset.nodeUuid;
    });

    const nextExecution = run.executions[nextExecutionIndex];

    if (nextExecution !== undefined) {
      const nextNode = nodes.find(
        (node) => node.uuid === nextExecution.nodeUuid,
      );

      if (nextNode !== undefined) {
        const previousExecutions = run.executions.slice(0, nextExecutionIndex);

        return [
          ...previousExecutions,
          await OrchestrationUtils.getExecutionFromNode(dependencies, {
            workspaceUuid: run.workspaceUuid,
            node: nextNode,
            status: "pending",
          }),
        ];
      }
    }

    const lastExecutionIndex = run.executions.length - 1;
    const lastExecution = run.executions[lastExecutionIndex];

    if (lastExecution === undefined || lastExecution.status === "pending") {
      return undefined;
    }

    const lastNodeUuid = lastExecution.nodeUuid;
    const lastNode = nodes.find((node) => node.uuid === lastNodeUuid);

    if (lastNode === undefined) {
      return undefined;
    }

    const isValidFallbackNode =
      lastExecution.status === "error" &&
      lastNode.fallbackOnFailure === true &&
      lastNode.fallbackChildUuid === reset.nodeUuid;

    const isValidChildNode =
      lastExecution.status === "success" &&
      lastExecution.nodeChildIndex !== null &&
      lastNode.childrenUuids[lastExecution.nodeChildIndex] === reset.nodeUuid;

    if (isValidFallbackNode === false && isValidChildNode === false) {
      return undefined;
    }

    const nextNode = nodes.find((node) => node.uuid === reset.nodeUuid);

    if (nextNode === undefined) {
      return undefined;
    }

    const previousExecutions = run.executions.slice(0, lastExecutionIndex);

    return [
      ...previousExecutions,
      {
        ...lastExecution,
        nextNodeUuid: nextNode.uuid,
      },
      await OrchestrationUtils.getExecutionFromNode(dependencies, {
        workspaceUuid: run.workspaceUuid,
        node: nextNode,
        status: "pending",
      }),
    ];
  }

  return undefined;
};
