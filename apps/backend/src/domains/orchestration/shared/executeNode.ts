import { castToJsonType, fromEntries, stringToDate } from "cargo-utils";

import type { CacheClient } from "../../../providers/cache/index.js";
import type { S3Client } from "../../../providers/s3/index.js";
import type { TemporalClient } from "../../../providers/temporal/index.js";
import { getDecryptedValuesWithAES, random } from "../../../utils/index.js";
import { runInVm } from "../../../utils/vm.js";
import type { BillingServices } from "../../billing/index.js";
import type { ConnectionServices } from "../../connection/index.js";
import { ConnectionUtils } from "../../connection/index.js";
import type {
  RevenueOrganizationServices,
  RevenueOrganizationTypes,
} from "../../revenueOrganization/index.js";
import type { SegmentationServices } from "../../segmentation/index.js";
import type { StorageServices } from "../../storage/index.js";
import type { OrchestrationDatastores } from "../datastores/index.js";
import type { OrchestrationTypes } from "../index.js";
import type {
  NativeIntegrationAllocateAction,
  NativeIntegrationBalanceAction,
  NativeIntegrationDelayAction,
  NativeIntegrationEndAction,
  NativeIntegrationGetModelSearchAction,
  NativeIntegrationGetModelVectorSearchAction,
  NativeIntegrationGroupAction,
  NativeIntegrationMemoryAction,
  NativeIntegrationModelCustomColumnAction,
  NativeIntegrationModelRecordAction,
  NativeIntegrationScoringAction,
  NativeIntegrationScript,
  NativeIntegrationSplitAction,
  NativeIntegrationSwitchAction,
  NativeIntegrationVariablesAction,
} from "../services/nativeIntegration/index.js";
import type { OrchestrationWorkers } from "../workers/index.js";
import { createBatch as sharedCreateBatch } from "./createBatch/index.js";
import { createRun as sharedCreateRun } from "./createRun.js";

const reshapeMemoryExpiresIn = (expiresIn: {
  value: number;
  unit: "second" | "minute" | "hour" | "day";
}): number => {
  if (expiresIn.unit === "day") {
    return expiresIn.value * 24 * 60 * 60;
  }

  if (expiresIn.unit === "hour") {
    return expiresIn.value * 60 * 60;
  }

  if (expiresIn.unit === "minute") {
    return expiresIn.value * 60;
  }

  return expiresIn.value;
};

export type ExecuteNodeDependencies = {
  datastores: OrchestrationDatastores;
  workers: OrchestrationWorkers;
  services: {
    connection: ConnectionServices;
    storage: StorageServices;
    segmentation: SegmentationServices;
    billing: BillingServices;
    revenueOrganization: RevenueOrganizationServices;
  };
  providers: {
    cache: CacheClient;
    s3: S3Client;
    temporal: TemporalClient;
  };
  encryption: {
    passphrase: string;
  };
};

export type ExecuteNodePayload = {
  workspaceUuid: string;
  workflowUuid: string;
  releaseUuid?: string;
  runUuid?: string;
  batchUuid?: string;
  modelUuid?: string;
  recordId?: string;
  execution: OrchestrationTypes.Execution;
  computedConfig: Record<string, unknown>;
  context: Record<string, unknown>;
  groupContext?: Record<string, unknown>;
  isDryExecution?: boolean;
};

export type ExecuteNodeResult =
  | {
      outcome: "executed";
      data: Record<string, any> | null;
      childIndex?: number;
      creditsUsedCount?: number;
      title?: string;
      iconUrl?: string;
    }
  | {
      outcome: "executingBatch";
      batchUuid: string;
      releaseUuid: string;
      childIndex?: number;
    }
  | {
      outcome: "executingRun";
      runUuid: string;
      releaseUuid: string;
      childIndex?: number;
    }
  | {
      outcome: "notExecuted";
      errorMessage: string;
      retry: boolean;
    }
  | { outcome: "executing" };

const isValidCondition = (value: any): boolean => {
  if (typeof value === "boolean") {
    return value;
  }

  if (typeof value === "string") {
    return !!value && !["no", "false"].includes(value.trim().toLowerCase());
  }

  if (typeof value === "number") {
    return value !== 0;
  }

  return !!value;
};

export const executeNode = async (
  dependencies: ExecuteNodeDependencies,
  payload: ExecuteNodePayload,
): Promise<ExecuteNodeResult> => {
  const { services, providers, encryption, datastores } = dependencies;
  const {
    workspaceUuid,
    workflowUuid,
    releaseUuid,
    runUuid,
    batchUuid,
    execution,
    modelUuid,
    recordId,
    isDryExecution,
    context,
    groupContext,
  } = payload;

  const computedConfig = stringToDate(payload.computedConfig);

  try {
    if (execution.nodeKind === "native") {
      if (execution.nodeActionSlug === "filter") {
        const isValid = isValidCondition(computedConfig.filter);

        return {
          outcome: "executed",
          data: { filter: isValid === true },
          title: isValid === true ? "✅ Is valid" : "❌ Is not valid",
          childIndex: isValid === true ? 0 : undefined,
        };
      }

      if (execution.nodeActionSlug === "branch") {
        const isValid = isValidCondition(computedConfig.condition);

        return {
          outcome: "executed",
          data: { condition: isValid === true },
          title:
            isValid === true
              ? "✅ Condition is matched"
              : "❌ Condition is not matched",
          childIndex: isValid === true ? 0 : 1,
        };
      }

      if (execution.nodeActionSlug === "split") {
        const { percentage } = computedConfig as NativeIntegrationSplitAction;

        const number = random(0, 100);

        return {
          outcome: "executed",
          data: { number },
          title:
            number < percentage
              ? `✅ ${number}% < ${percentage}%`
              : `❌ ${number}% >= ${percentage}%`,
          childIndex: number < percentage ? 0 : 1,
        };
      }

      if (execution.nodeActionSlug === "modelCustomColumn") {
        const {
          modelUuid,
          mappings = [],
          id,
        } = computedConfig as NativeIntegrationModelCustomColumnAction;

        if (id === undefined || id === null) {
          return {
            outcome: "notExecuted",
            errorMessage: "Record ID is not defined",
            retry: false,
          };
        }

        const result = await services.storage.column.insertValues({
          workspaceUuid,
          modelUuid,
          mappings,
          id,
        });

        if (result.outcome === "notInserted") {
          return {
            outcome: "notExecuted",
            errorMessage: "Unable to insert values",
            retry: false,
          };
        }

        return {
          outcome: "executed",
          data: {
            _id: id,
            custom: fromEntries(
              mappings.map((mapping) => [mapping.columnSlug, mapping.value]),
            ),
          },
          title: "✅ Record upserted",
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "modelSearch") {
        const {
          modelUuid,
          filter = { groups: [], conjonction: "and" },
          limit = 1,
          sort,
        } = computedConfig as NativeIntegrationGetModelSearchAction;

        const segmentFetchResult = await services.segmentation.segment.fetch({
          workspaceUuid,
          filter,
          sort,
          limit,
          modelUuid,
          enrich: false,
          sync: false,
        });

        if (segmentFetchResult.outcome === "notFetched") {
          return {
            outcome: "notExecuted",
            errorMessage:
              segmentFetchResult.reason === "invalidFilter" ||
              segmentFetchResult.reason === "failedToFetchFromClient"
                ? segmentFetchResult.errorMessage
                : "Unable to fetch records",
            retry: false,
          };
        }

        return {
          outcome: "executed",
          data: segmentFetchResult.records,
          title:
            segmentFetchResult.records.length === 0
              ? "❌ No records found"
              : `✅ ${segmentFetchResult.records.length} records found`,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "modelVectorSearch") {
        const {
          modelUuid,
          filter = { groups: [], conjonction: "and" },
          semanticQuery,
          limit = 5,
        } = computedConfig as NativeIntegrationGetModelVectorSearchAction;

        const segmentFetchResult = await services.segmentation.segment.fetch({
          workspaceUuid,
          filter,
          limit,
          semanticQuery,
          modelUuid,
          enrich: false,
          sync: false,
        });

        if (segmentFetchResult.outcome === "notFetched") {
          return {
            outcome: "notExecuted",
            errorMessage:
              segmentFetchResult.reason === "invalidFilter" ||
              segmentFetchResult.reason === "failedToFetchFromClient"
                ? segmentFetchResult.errorMessage
                : "Unable to fetch records",
            retry: false,
          };
        }

        return {
          outcome: "executed",
          data: segmentFetchResult.records,
          title:
            segmentFetchResult.records.length === 0
              ? "❌ No records found"
              : `✅ ${segmentFetchResult.records.length} records found`,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "variables") {
        const { variables = [] } =
          computedConfig as NativeIntegrationVariablesAction;

        return {
          outcome: "executed",
          title: `✅ Executed`,
          data: fromEntries(
            variables.map((value) => {
              const castedValue = castToJsonType(value.value, value.type);

              return [
                value.name,
                castedValue !== undefined ? castedValue : null,
              ];
            }),
          ),
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "scoring") {
        const { criterias = [] } =
          computedConfig as NativeIntegrationScoringAction;

        const data = criterias.reduce<{
          score: number;
          criterias: { name: string; score: number }[];
        }>(
          (data, criteria) => {
            const isValid = isValidCondition(criteria.value);

            return {
              score: isValid == true ? data.score + criteria.score : data.score,
              criterias: [
                ...data.criterias,
                {
                  name: criteria.name,
                  score: isValid === true ? criteria.score : 0,
                },
              ],
            };
          },
          { score: 0, criterias: [] },
        );

        return {
          outcome: "executed",
          data,
          title: `✅ ${data.score}`,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "switch") {
        const { routes = [] } = computedConfig as NativeIntegrationSwitchAction;

        const childIndex = routes.findIndex((route) =>
          isValidCondition(route.value),
        );

        const route = routes[childIndex];

        return {
          outcome: "executed",
          data: {
            routeName: route !== undefined ? route.name : undefined,
          },
          title:
            route === undefined
              ? "❌ No route matched"
              : `✅ Route ${route.name} matched`,
          childIndex,
        };
      }

      if (execution.nodeActionSlug === "allocate") {
        const config = computedConfig as NativeIntegrationAllocateAction;

        if (typeof config.recordId !== "string") {
          return {
            outcome: "notExecuted",
            errorMessage: "Invalid record ID",
            retry: false,
          };
        }

        const connectors = await services.connection.connector.list({
          workspaceUuid,
        });

        const getMemberConnectorsIds = (
          memberConnectors: Array<{ uuid: string; userId: string }>,
        ) => {
          return memberConnectors.reduce<Record<string, string>>(
            (acc, { uuid, userId }) => {
              const connector = connectors.find(
                (connector) => connector.uuid === uuid,
              );

              if (connector) {
                return { ...acc, [connector.slug]: userId };
              }

              return acc;
            },
            {},
          );
        };

        const getMemberDetails = (member: RevenueOrganizationTypes.Member) => {
          const memberConnectorIds = getMemberConnectorsIds(member.connectors);

          return {
            uuid: member.uuid,
            firstName: member.firstName,
            lastName: member.lastName,
            profileImage: member.profileImage,
            email: member.email,
            ids: memberConnectorIds,
            meta: member.meta,
          };
        };

        const getTerritoryDetails = (
          territory: RevenueOrganizationTypes.Territory,
          members: RevenueOrganizationTypes.Member[],
          fallbackMember: RevenueOrganizationTypes.Member | undefined,
        ) => {
          return {
            uuid: territory.uuid,
            label: territory.label,
            description: territory.description,
            members: territory.membersUuids.map((memberUuid) => {
              const member = members.find((member) => {
                return member.uuid === memberUuid;
              });

              if (member === undefined) {
                return null;
              }

              return getMemberDetails(member);
            }),
            fallbackMember:
              fallbackMember !== undefined
                ? getMemberDetails(fallbackMember)
                : null,
          };
        };

        let additionalDetails: Record<string, unknown> = {};

        let createAllocationResult:
          | {
              outcome: "created";
              allocation: RevenueOrganizationTypes.Allocation;
            }
          | { outcome: "notCreated"; reason: "noMembersAvailable" }
          | { outcome: "notCreated"; reason: "invalidAllocator" };

        if (config.type === "email") {
          const member = await services.revenueOrganization.member.get({
            workspaceUuid,
            email: config.email,
          });

          if (member === undefined) {
            return {
              outcome: "notExecuted",
              errorMessage: "No matching member",
              retry: false,
            };
          }

          createAllocationResult =
            await services.revenueOrganization.allocation.create({
              workspaceUuid,
              allocatedBy: {
                allocator: "workflow",
                workflowUuid,
                workflowNodeUuid: execution.nodeUuid,
                workflowRunUuid: runUuid,
                membersUuids: [member.uuid],
                modelUuid,
                recordId,
              },
              capacityUuid: config.capacityUuid,
              recordId: config.recordId,
              isTest: isDryExecution,
            });
        } else if (config.type === "matching") {
          const members = await services.revenueOrganization.member.list({
            workspaceUuid,
          }); // to be improved

          const member = members.find((member) => {
            return member.connectors.some((connector) => {
              return (
                connector.userId === config.matchingMemberId &&
                connector.uuid === config.matchingConnectorUuid
              );
            });
          });

          if (member === undefined) {
            return {
              outcome: "notExecuted",
              errorMessage: "No matching member",
              retry: false,
            };
          }

          createAllocationResult =
            await services.revenueOrganization.allocation.create({
              workspaceUuid,
              allocatedBy: {
                allocator: "workflow",
                workflowUuid,
                workflowNodeUuid: execution.nodeUuid,
                workflowRunUuid: runUuid,
                membersUuids: [member.uuid],
                modelUuid,
                recordId,
              },
              capacityUuid: config.capacityUuid,
              recordId: config.recordId,
              isTest: isDryExecution,
            });
        } else if (config.type === "members") {
          createAllocationResult =
            await services.revenueOrganization.allocation.create({
              workspaceUuid,
              allocatedBy: {
                allocator: "workflow",
                workflowUuid,
                workflowNodeUuid: execution.nodeUuid,
                workflowRunUuid: runUuid,
                membersUuids: config.membersUuids,
                fallbackMemberUuid: config.fallbackMemberUuid,
                modelUuid,
                recordId,
              },
              capacityUuid: config.capacityUuid,
              recordId: config.recordId,
              isTest: isDryExecution,
            });
        } else {
          const [territory, members] = await Promise.all([
            services.revenueOrganization.territory.get({
              workspaceUuid,
              uuid: config.territoryUuid,
            }),
            services.revenueOrganization.member.list({
              workspaceUuid,
            }),
          ]);

          if (territory === undefined) {
            return {
              outcome: "notExecuted",
              errorMessage: "Territory not found",
              retry: false,
            };
          }

          const fallbackMember =
            territory.fallbackMemberUuid !== null
              ? members.find((member) => {
                  return member.uuid === territory.fallbackMemberUuid;
                })
              : undefined;

          additionalDetails = {
            territory: getTerritoryDetails(territory, members, fallbackMember),
          };

          createAllocationResult =
            await services.revenueOrganization.allocation.create({
              workspaceUuid,
              allocatedBy: {
                allocator: "territory",
                workflowUuid,
                workflowNodeUuid: execution.nodeUuid,
                workflowRunUuid: runUuid,
                territoryUuid: config.territoryUuid,
                modelUuid,
                recordId,
              },
              capacityUuid: config.capacityUuid,
              recordId: config.recordId,
              isTest: isDryExecution,
            });
        }

        if (createAllocationResult.outcome === "notCreated") {
          if (createAllocationResult.reason === "noMembersAvailable") {
            return {
              outcome: "notExecuted",
              errorMessage: "No members available",
              retry: true,
            };
          }

          return {
            outcome: "notExecuted",
            errorMessage: "Unable to allocate member",
            retry: false,
          };
        }

        const { allocation } = createAllocationResult;

        const member = await services.revenueOrganization.member.get({
          workspaceUuid,
          uuid: allocation.memberUuid,
        });

        if (member === undefined) {
          return {
            outcome: "notExecuted",
            errorMessage: "Member not found",
            retry: false,
          };
        }

        return {
          outcome: "executed",
          data: {
            ...additionalDetails,
            member: getMemberDetails(member),
            isFallbackMember: allocation.isFallbackMember,
          },
          title: `✅ ${member.email}`,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "balance") {
        const { routes = [] } =
          computedConfig as NativeIntegrationBalanceAction;

        const cacheKey = `orchestration:nodes:${execution.nodeUuid}:count`;

        const cachedCount = await providers.cache.get<number>(cacheKey);
        const count = cachedCount !== undefined ? cachedCount : 0;

        const childIndex = count % routes.length;

        await providers.cache.set(cacheKey, count + 1);

        const route = routes[childIndex];

        return {
          outcome: "executed",
          data: {
            routeName: route !== undefined ? route.name : undefined,
          },
          title:
            route === undefined
              ? "❌ No route matched"
              : `✅ Route ${route.name} matched`,
          childIndex,
        };
      }

      if (execution.nodeActionSlug === "memory") {
        const config = computedConfig as NativeIntegrationMemoryAction;
        const scope =
          config.scope === "workflow" ? workflowUuid : workspaceUuid;

        const cacheKey = `orchestration:${
          config.scope
        }:${scope}:keys:${JSON.stringify(config.key)}`;

        if (config.action === "get") {
          const result = await providers.cache.get(cacheKey);

          return {
            outcome: "executed",
            data: { result },
            title: result === undefined ? "❌ No data found" : "✅ Data found",
            childIndex: 0,
          };
        }

        if (config.action === "remove") {
          await providers.cache.remove(cacheKey);

          return {
            outcome: "executed",
            data: { result: undefined },
            title: "✅ Data removed",
            childIndex: 0,
          };
        }

        if (config.action === "decrement") {
          const result = await providers.cache.decrement(
            cacheKey,
            reshapeMemoryExpiresIn(config.expiresIn),
          );

          return {
            outcome: "executed",
            data: { result },
            title: `✅ ${result}`,
            childIndex: 0,
          };
        }

        if (config.action === "increment") {
          const result = await providers.cache.increment(
            cacheKey,
            reshapeMemoryExpiresIn(config.expiresIn),
          );

          return {
            outcome: "executed",
            data: { result },
            title: `✅ ${result}`,
            childIndex: 0,
          };
        }

        if (config.action === "set") {
          const result = await providers.cache.set(
            cacheKey,
            config.value,
            reshapeMemoryExpiresIn(config.expiresIn),
          );

          return {
            outcome: "executed",
            data: { result },
            title: "✅ Data set",
            childIndex: 0,
          };
        }

        if (config.action === "getOrSet") {
          const result = await providers.cache.get(cacheKey);

          if (result !== undefined) {
            return {
              outcome: "executed",
              data: { result },
              title: "✅ Data found",
              childIndex: 0,
            };
          }

          const setResult = await providers.cache.set(
            cacheKey,
            config.value,
            reshapeMemoryExpiresIn(config.expiresIn),
          );

          return {
            outcome: "executed",
            data: { result: setResult },
            title: "✅ Data set",
            childIndex: 0,
          };
        }

        const result = await providers.cache.get(cacheKey);

        return {
          outcome: "executed",
          data: { result },
          title: result === undefined ? "❌ Data not found" : "✅ Data found",
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "script") {
        const config = computedConfig as NativeIntegrationScript;

        try {
          const result = await runInVm(config.script, {
            nodes: context,
            parentNodes: groupContext,
          });

          return {
            outcome: "executed",
            title: "✅ Executed",
            data: { result },
            childIndex: 0,
          };
        } catch (error) {
          return {
            outcome: "notExecuted",
            errorMessage: (error as Error).message,
            retry: true,
          };
        }
      }

      if (execution.nodeActionSlug === "group") {
        const config = computedConfig as NativeIntegrationGroupAction;

        if (runUuid === undefined || releaseUuid === undefined) {
          return {
            outcome: "notExecuted",
            errorMessage: "Run not found",
            retry: false,
          };
        }

        const items = Array.isArray(config.items)
          ? config.items
          : [config.items];

        if (items.length === 0) {
          return {
            outcome: "executed",
            data: [],
            title: "❌ No items",
            childIndex: 0,
          };
        }

        const workflow = await datastores.workflow.get(
          { workspaceUuid, uuid: workflowUuid },
          { enrich: false },
        );

        if (workflow === undefined) {
          return {
            outcome: "notExecuted",
            errorMessage: "Workflow not found",
            retry: false,
          };
        }

        const release = await datastores.release.get({
          workspaceUuid,
          uuid: releaseUuid,
        });

        if (release === undefined) {
          return {
            outcome: "notExecuted",
            errorMessage: "Release not found",
            retry: false,
          };
        }

        const createBatchResult = await sharedCreateBatch(dependencies, {
          workflow,
          release,
          data: {
            kind: "group",
            items,
            parentBatchUuid: batchUuid,
            parentRunUuid: runUuid,
            parentNodeUuid: execution.nodeUuid,
          },
        });

        if (createBatchResult.outcome === "notCreated") {
          return {
            outcome: "notExecuted",
            errorMessage: "Unable to create batch",
            retry: false,
          };
        }

        return {
          outcome: "executingBatch",
          batchUuid: createBatchResult.batch.uuid,
          releaseUuid: createBatchResult.batch.releaseUuid,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "modelRecord") {
        const config = computedConfig as NativeIntegrationModelRecordAction;

        const data = Array.isArray(config.data) ? config.data : [config.data];

        const areRecordObjects = data.every(
          (record) => typeof record === "object",
        );

        if (areRecordObjects === false) {
          return {
            outcome: "notExecuted",
            errorMessage: "Records must be objects",
            retry: false,
          };
        }

        if (data.length === 0) {
          return {
            outcome: "executed",
            data: null,
            title: "❌ No records ingested",
            childIndex: 0,
          };
        }

        const result = await services.storage.record.ingest({
          workspaceUuid,
          modelUuid: config.modelUuid,
          data,
        });

        if (result.outcome === "notIngested") {
          if (result.reason === "recordTooLarge") {
            return {
              outcome: "notExecuted",
              errorMessage: "One of the records is too large (> 1MB)",
              retry: false,
            };
          } else {
            return {
              outcome: "notExecuted",
              errorMessage: "Unable to ingest records",
              retry: false,
            };
          }
        }

        const records =
          result.mode === "async" ? result.rawRecords : result.records;

        return {
          outcome: "executed",
          data: records.map((record) => {
            return {
              _id: record["_ingest_id"],
              ...record,
            };
          }),
          title: `${data.length} records ingested`,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "delay") {
        const config = computedConfig as NativeIntegrationDelayAction;

        return {
          outcome: "executed",
          data: { minutes: config.minutes },
          title: `✅ Waited ${config.minutes} minutes`,
          childIndex: 0,
        };
      }

      if (execution.nodeActionSlug === "end") {
        const { variables = [] } = computedConfig as NativeIntegrationEndAction;

        return {
          outcome: "executed",
          title: `✅ Finished`,
          data: fromEntries(
            variables.map((value) => {
              const castedValue = castToJsonType(value.value, value.type);

              return [
                value.name,
                castedValue !== undefined ? castedValue : null,
              ];
            }),
          ),
          childIndex: 0,
        };
      }

      return {
        outcome: "executed",
        data: null,
        childIndex: 0,
      };
    }

    if (execution.nodeKind === "connector") {
      const connector = await services.connection.connector.get({
        workspaceUuid,
        uuid: execution.nodeConnectorUuid,
      });

      if (connector === undefined) {
        return {
          outcome: "notExecuted",
          errorMessage: "Unable to retrieve connector",
          retry: false,
        };
      }

      const integration = await services.connection.integration.get({
        workspaceUuid,
        slug: connector.integrationSlug,
      });

      if (integration === undefined) {
        return {
          outcome: "notExecuted",
          errorMessage: "Unable to retrieve integration",
          retry: false,
        };
      }

      if (
        integration.manifest.connector.caching !== undefined &&
        integration.manifest.connector.caching.isCompatible === true &&
        connector.cacheTtlMilliseconds !== null
      ) {
        const cachedResult =
          await services.connection.integration.getCachedActionResult({
            workspaceUuid,
            connectorUuid: execution.nodeConnectorUuid,
            actionSlug: execution.nodeActionSlug,
            config: computedConfig,
            ttlMilliseconds: connector.cacheTtlMilliseconds,
          });

        if (cachedResult !== undefined) {
          return {
            outcome: "executed",
            data: cachedResult.data !== undefined ? cachedResult.data : null,
            childIndex: cachedResult.childIndex,
            creditsUsedCount: 0,
            title: cachedResult.title,
            iconUrl: cachedResult.iconUrl,
          };
        }
      }

      if (connector.useCredits === true) {
        const hasCredits = await services.billing.subscription.hasCredits({
          workspaceUuid,
        });

        if (hasCredits === false) {
          return {
            outcome: "notExecuted",
            errorMessage: "Not enough credits",
            retry: false,
          };
        }
      }

      const decryptedConnectorConfig = getDecryptedValuesWithAES(
        connector.config,
        ConnectionUtils.getConnectorEncryptionPassphrase(
          encryption.passphrase,
          connector,
        ),
      );

      const integrationAction = integration.actions[execution.nodeActionSlug];

      if (integrationAction === undefined) {
        return {
          outcome: "notExecuted",
          errorMessage: "Unable to retrieve action",
          retry: false,
        };
      }

      const validateResult = await integrationAction.validate({
        config: computedConfig,
      });

      if (validateResult.outcome === "notValid") {
        return {
          outcome: "notExecuted",
          errorMessage: validateResult.errorMessage,
          retry: false,
        };
      }

      const executeResult = await integrationAction.execute({
        connector: {
          uuid: connector.uuid,
          workspaceUuid,
          config: decryptedConnectorConfig,
        },
        config: computedConfig,
        isDryExecution,
      });

      if (executeResult.outcome === "executing") {
        return { outcome: "executing" };
      }

      const {
        data,
        unitsCount = services.connection.integration.getUnitsCount({ data }),
        title,
        iconUrl,
        childIndex = 0,
      } = executeResult;

      let creditsUsedCount: number | undefined = undefined;

      if (connector.useCredits === true) {
        const integrationManifestActionCredits =
          integration.manifest.actions[execution.nodeActionSlug]?.credits;

        if (
          integrationManifestActionCredits !== undefined &&
          unitsCount !== undefined
        ) {
          creditsUsedCount =
            services.connection.integration.getCreditsUsedCount({
              creditsCosts: integrationManifestActionCredits.costs,
              unitsCount,
              config: computedConfig,
            });

          await services.billing.usage.create({
            workspaceUuid,
            workflowUuid,
            integrationSlug: connector.integrationSlug,
            connectorUuid: connector.uuid,
            unit: "integration.credits",
            count: creditsUsedCount,
          });
        }
      }

      if (
        integration.manifest.connector.caching !== undefined &&
        integration.manifest.connector.caching.isCompatible === true &&
        connector.cacheTtlMilliseconds !== null
      ) {
        await services.connection.integration.setCachedActionResult({
          workspaceUuid,
          connectorUuid: execution.nodeConnectorUuid,
          actionSlug: execution.nodeActionSlug,
          config: computedConfig,
          result: {
            data,
            childIndex,
            title,
            iconUrl,
          },
        });
      }

      return {
        outcome: "executed",
        data: data !== undefined ? data : null,
        childIndex,
        creditsUsedCount,
        title,
        iconUrl,
      };
    }

    const tool = await datastores.tool.get({
      workspaceUuid,
      uuid: execution.nodeToolUuid,
    });

    if (tool === undefined) {
      return {
        outcome: "notExecuted",
        errorMessage: "Unable to retrieve tool",
        retry: false,
      };
    }

    if (tool.workflowUuid === workflowUuid) {
      return {
        outcome: "notExecuted",
        errorMessage: "Cannot call the same tool recursively",
        retry: false,
      };
    }

    const createRunResult = await sharedCreateRun(dependencies, {
      workspaceUuid,
      workflowUuid: tool.workflowUuid,
      releaseUuid:
        execution.nodeReleaseUuid !== null
          ? execution.nodeReleaseUuid
          : undefined,
      parentUuid: runUuid,
      parentBatchUuid: batchUuid,
      parentNodeUuid: execution.nodeUuid,
      data: computedConfig,
    });

    if (createRunResult.outcome === "notCreated") {
      if (createRunResult.reason === "invalidData") {
        return {
          outcome: "notExecuted",
          errorMessage: createRunResult.errorMessage,
          retry: false,
        };
      }

      return {
        outcome: "notExecuted",
        errorMessage: "Unable to create run",
        retry: false,
      };
    }

    return {
      outcome: "executingRun",
      runUuid: createRunResult.run.uuid,
      releaseUuid: createRunResult.run.releaseUuid,
      childIndex: 0,
    };
  } catch (error) {
    return {
      outcome: "notExecuted",
      errorMessage: (error as Error).message,
      retry: true,
    };
  }
};
