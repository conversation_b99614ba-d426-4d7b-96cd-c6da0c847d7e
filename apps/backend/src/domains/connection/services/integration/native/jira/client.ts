import type { AxiosError } from "axios";
import axios from "axios";
import { stringifyJson } from "cargo-utils";

export type CreateIssuePayload = {
  fields: {
    project: {
      key: string;
    };
    issuetype: {
      id: string;
    };
    summary: string;
    description: {
      content: [
        {
          content: [
            {
              text: string;
              type: "text";
            },
          ];
          type: "paragraph";
        },
      ];
      type: "doc";
      version: 1;
    };
  };
};

export type CreateJiraIssueResponse = {
  id: string;
  key: string;
  self: string;
  transition: {
    errorCollection: {
      errorMessages: string[];
      errors: Record<string, string>;
      status: number;
    };
    status: number;
    warningCollection: {
      warnings: string[];
    };
  };
};

export type JiraGetIssueResponse = {
  changelog: Record<string, unknown>;
  editmeta: Record<string, unknown>;
  expand: string;
  fields: Record<string, unknown>;
  fieldsToInclude: Record<string, unknown>;
  id: string;
  key: string;
  names: Record<string, unknown>;
  operations: Record<string, unknown>;
  properties: Record<string, unknown>;
  renderedFields: Record<string, unknown>;
  schema: Record<string, unknown>;
  self: string;
  transitions: Record<string, unknown>[];
  versionedRepresentations: Record<string, unknown>;
};

export type JiraGetIssueTypesResponse = {
  avatarId: number;
  description: string;
  hierarchyLevel: number;
  iconUrl: string;
  id: string;
  name: string;
  self: string;
  subtask: boolean;
}[];

export type JiraGetProjectsResponse = {
  isLast: boolean;
  maxResults: number;
  nextPage: string;
  self: string;
  startAt: number;
  total: number;
  values: {
    avatarUrls: {
      "16x16": string;
      "24x24": string;
      "32x32": string;
      "48x48": string;
    };
    id: string;
    insight: {
      lastIssueUpdateTime: string;
      totalIssueCount: number;
    };
    key: string;
    name: string;
    projectCategory: {
      description: string;
      id: string;
      name: string;
      self: string;
    };
    self: string;
    simplified: boolean;
    style: string;
  }[];
};

export type JiraClient = {
  isAuthenticated: () => Promise<boolean>;
  createIssue: (
    payload: CreateIssuePayload,
  ) => Promise<CreateJiraIssueResponse>;
  getIssue: (issueIdOrKey: string) => Promise<JiraGetIssueResponse>;
  getIssueTypes: () => Promise<JiraGetIssueTypesResponse>;
  getProjects: () => Promise<JiraGetProjectsResponse>;
};

export type JiraClientDependencies = {
  domain: string;
  apiKey: string;
  userEmail: string;
};

export const buildJiraClient = (
  dependencies: JiraClientDependencies,
): JiraClient => {
  const encodedCredentials = Buffer.from(
    `${dependencies.userEmail}:${dependencies.apiKey}`,
  ).toString("base64");

  const headers = {
    Authorization: `Basic ${encodedCredentials}`,
    Accept: "application/json",
    "Content-Type": "application/json",
  };

  const client = axios.create({
    baseURL: `https://${dependencies.domain}.atlassian.net/rest/api/3`,
    headers,
  });

  return {
    isAuthenticated: async () => {
      try {
        await client.get("/dashboard");
        return true;
      } catch (error) {
        const axiosError = error as AxiosError;
        if (
          axiosError.response !== undefined &&
          axiosError.response.status === 401
        ) {
          return false;
        }

        return true;
      }
    },
    createIssue: async (payload) => {
      try {
        const response = await client.post("/issue", payload);
        return response.data;
      } catch (error) {
        const axiosError = error as AxiosError;

        if (
          axiosError.response !== undefined &&
          typeof axiosError.response.data === "object"
        ) {
          throw new Error(stringifyJson(axiosError.response.data));
        }

        throw error;
      }
    },
    getIssue: async (issueIdOrKey: string) => {
      try {
        const response = await client.get(`/issue/${issueIdOrKey}`);
        return response.data;
      } catch (error) {
        const axiosError = error as AxiosError;

        if (
          axiosError.response !== undefined &&
          typeof axiosError.response.data === "object"
        ) {
          throw new Error(stringifyJson(axiosError.response.data));
        }

        throw error;
      }
    },
    getIssueTypes: async () => {
      try {
        const response = await client.get("/issuetype");
        return response.data;
      } catch (error) {
        const axiosError = error as AxiosError;

        if (
          axiosError.response !== undefined &&
          typeof axiosError.response.data === "object"
        ) {
          throw new Error(stringifyJson(axiosError.response.data));
        }

        throw error;
      }
    },
    getProjects: async () => {
      try {
        const response = await client.get("/project/search");
        return response.data;
      } catch (error) {
        const axiosError = error as AxiosError;

        if (
          axiosError.response !== undefined &&
          typeof axiosError.response.data === "object"
        ) {
          throw new Error(stringifyJson(axiosError.response.data));
        }

        throw error;
      }
    },
  };
};
