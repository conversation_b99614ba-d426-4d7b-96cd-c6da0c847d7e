import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { buildJiraClient } from "./client.js";
import type { ConnectorConfig } from "./jira.js";

export type AutocompletePayload =
  ConnectionTypes.IntegrationAutocompletePayload<
    ConnectorConfig,
    "listObjectFieldValues",
    { fieldName: "issueType" | "projectKey" }
  >;

export type AutocompleteResult = ConnectionTypes.IntegrationAutocompleteResult;

export const autocomplete = async (
  _dependencies: ConnectionServicesDependencies,
  payload: AutocompletePayload,
): Promise<AutocompleteResult> => {
  const { connector } = payload;

  const jiraClient = buildJiraClient({
    apiKey: connector.config.apiKey,
    domain: connector.config.domain,
    userEmail: connector.config.userEmail,
  });

  if (payload.slug === "listObjectFieldValues") {
    if (payload.params.fieldName === "issueType") {
      const issueTypes = await jiraClient.getIssueTypes();

      return {
        results: issueTypes.map((issueType) => {
          return {
            label: issueType.name,
            value: issueType.id,
          };
        }),
      };
    }

    if (payload.params.fieldName === "projectKey") {
      const response = await jiraClient.getProjects();

      return {
        results: response.values.map((project) => {
          return {
            label: project.name,
            value: project.key,
          };
        }),
      };
    }
  }

  return {
    results: [],
  };
};
