import { z } from "zod";

export const zodConnectorConfig = z.object({
  apiKey: z.string(),
  domain: z.string(),
  userEmail: z.string(),
});

export type ConnectorConfig = z.infer<typeof zodConnectorConfig>;

export const zodWriteActionConfig = z.object({
  fields: z.array(
    z.union([
      z.object({
        name: z.enum(["summary", "description"]),
        value: z.string(),
      }),
      z.object({
        name: z.enum(["projectKey", "issueType"]),
        autocompleteValue: z.string(),
      }),
    ]),
  ),
});

export type WriteActionConfig = z.infer<typeof zodWriteActionConfig>;

export const zodSearchActionConfig = z.object({
  issueIdOrKey: z.string(),
});

export type SearchActionConfig = z.infer<typeof zodSearchActionConfig>;
