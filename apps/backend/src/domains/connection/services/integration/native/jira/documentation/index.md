# JIRA

## Overview

This doc outlines how to use the JIRA connector to create and retrieve issues in Atlassian JIRA.

---

## Setup

**Create connector**  
From the settings panel or from the node catalog, search for the JIRA integration and navigate to the 'create connector' view.

**Retrieve your JIRA credentials**

- **JIRA Domain**: Your JIRA instance domain (e.g., `yourcompany` for `yourcompany.atlassian.net`).
- **User Email**: The email address associated with your JIRA account.
- **API Key**: Generate an API token from your Atlassian account ([API tokens](https://id.atlassian.com/manage-profile/security/api-tokens)).

**Configure the connector**  
Enter your JIRA domain, user email, and API key into the dedicated fields in Cargo, then save the connector.

---

## Actions

### Write - Create a new issue in JIRA

Use the **write** action to create a new issue in your JIRA project. You can map the following fields:

| Field       | Required | Description                                                  |
| ----------- | -------- | ------------------------------------------------------------ |
| projectKey  | Yes      | The key of the project to create the issue in (autocomplete) |
| summary     | Yes      | The summary/title of the issue                               |
| issueType   | Yes      | The type of the issue (e.g., Bug, Task) (autocomplete)       |
| description | No       | The description of the issue                                 |

**Example mapping:**

- projectKey: `CARGO`
- summary: `Fix login bug`
- issueType: `Bug`
- description: `Users are unable to log in with SSO.`

The action will return the created issue's key and details.

### Search - Retrieve an issue from JIRA

Use the **search** action to retrieve an issue by its ID or key.

| Field        | Required | Description                     |
| ------------ | -------- | ------------------------------- |
| issueIdOrKey | Yes      | The ID or key of the JIRA issue |

**Example mapping:**

- issueIdOrKey: `CARGO-123`

The action will return the full details of the issue, including status, summary, description, and more.

---

## FAQ

**Which rate limits should I set for this connector?**  
JIRA's REST API has rate limiting based on your plan and instance. For more details, see the [JIRA API rate limits documentation](https://developer.atlassian.com/cloud/jira/platform/rate-limiting/).

**Where do I find my API key?**  
You can generate and manage API tokens from your Atlassian account's [API tokens page](https://id.atlassian.com/manage-profile/security/api-tokens).

**What permissions are required?**  
The API key must have permission to create and view issues in the target JIRA project.
