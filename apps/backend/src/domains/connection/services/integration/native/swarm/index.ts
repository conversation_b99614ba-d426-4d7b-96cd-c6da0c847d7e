import path from "path";
import { fileURLToPath } from "url";

import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { validateAction } from "../../utils.js";
import { searchAction } from "./actions/search.js";
import { authenticate } from "./authenticate.js";
import { autocomplete } from "./autocomplete.js";
import { manifest } from "./manifest.js";
import { modelCount } from "./model/count.js";
import { modelFetch } from "./model/fetch.js";
import { zodSearchActionConfig } from "./swarm.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const buildSwarmIntegration = (
  dependencies: ConnectionServicesDependencies,
): ConnectionTypes.Integration => {
  return {
    documentationPath: path.join(__dirname, "documentation"),
    manifest,
    actions: {
      search: {
        execute: (payload) => searchAction(dependencies, payload),
        validate: async (payload) =>
          validateAction(payload.config, zodSearchActionConfig),
      },
    },
    authenticate: (payload) => authenticate(dependencies, payload),
    model: {
      fetch: (payload) => modelFetch(dependencies, payload),
      count: (payload) => modelCount(dependencies, payload),
    },
    autocomplete: (payload) => autocomplete(dependencies, payload),
  };
};
