import type { ConnectionTypes } from "../../../../index.js";

const icon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2545_20294)">
    <path
      d="M0 6C0 4.4087 0.632141 2.88258 1.75736 1.75736C2.88258 0.632141 4.4087 0 6 0L18 0C19.5913 0 21.1174 0.632141 22.2426 1.75736C23.3679 2.88258 24 4.4087 24 6V18C24 19.5913 23.3679 21.1174 22.2426 22.2426C21.1174 23.3679 19.5913 24 18 24H6C4.4087 24 2.88258 23.3679 1.75736 22.2426C0.632141 21.1174 0 19.5913 0 18L0 6Z"
      fill="#1868DB" />
    <path
      d="M9.05031 15.4341H7.73331C5.74531 15.4341 4.32031 14.2161 4.32031 12.4341H11.4053C11.7723 12.4341 12.0103 12.6941 12.0103 13.0641V20.1941C10.2383 20.1941 9.05031 18.7591 9.05031 16.7601V15.4341ZM12.5503 11.8911H11.2323C9.24531 11.8911 7.81931 10.6951 7.81931 8.91314H14.9043C15.2713 8.91314 15.5313 9.15214 15.5313 9.52114V16.6511C13.7593 16.6511 12.5503 15.2161 12.5503 13.2171V11.8911ZM16.0703 8.36914H14.7533C12.7663 8.36914 11.3403 7.15214 11.3403 5.36914H18.4253C18.7923 5.36914 19.0303 5.63114 19.0303 5.97914V13.1081C17.2593 13.1081 16.0703 11.6731 16.0703 9.67414V8.36914Z"
      fill="white" />
  </g>
  <defs>
    <clipPath id="clip0_2545_20294">
      <rect width="24" height="24" fill="white" />
    </clipPath>
  </defs>
</svg>`;

export const manifest: ConnectionTypes.IntegrationManifest = {
  name: "JIRA",
  slug: "jira",
  url: "https://www.atlassian.com/software/jira",
  category: "support",
  icon,
  color: "#1868DB",
  description: "Integration with Atlassian JIRA for issue management.",
  autocompletes: [
    {
      slug: "listObjectFieldValues",
      params: {
        jsonSchema: {
          type: "object",
          properties: {
            fieldName: { type: "string" },
          },
          required: ["fieldName"],
        },
      },
    },
  ],
  dynamicSchemas: [],
  connector: {
    rateLimit: {
      max: 1000,
      unit: "hour",
    },
    config: {
      jsonSchema: {
        type: "object",
        properties: {
          apiKey: {
            title: "API Key",
            $ref: "#/definitions/encryption",
          },
          domain: {
            title: "JIRA Domain",
            type: "string",
          },
          userEmail: {
            title: "User Email",
            type: "string",
          },
        },
        required: ["apiKey", "domain", "userEmail"],
      },
      uiSchema: {
        apiKey: {
          "ui:widget": "EncryptionWidget",
        },
      },
    },
  },
  actions: {
    write: {
      name: "Write",
      description: "Create a new issue in JIRA",
      config: {
        jsonSchema: {
          type: "object",
          properties: {
            fields: {
              type: "array",
              title: "Fields",
              items: {
                type: "object",
                properties: {
                  name: {
                    title: "Name",
                    type: "string",
                    anyOf: [
                      {
                        title: "Project Key",
                        const: "projectKey",
                        description:
                          "The key of the project to create the issue in",
                      },
                      {
                        title: "Summary",
                        const: "summary",
                        description: "The summary of the issue to create",
                      },
                      {
                        title: "Issue Type",
                        const: "issueType",
                        description: "The type of the issue to create",
                      },
                      {
                        title: "Description",
                        const: "description",
                        description: "The description of the issue to create",
                      },
                    ],
                  },
                },
                allOf: [
                  {
                    if: {
                      properties: {
                        name: {
                          anyOf: [
                            { const: "projectKey" },
                            { const: "issueType" },
                          ],
                        },
                      },
                    },
                    then: {
                      properties: {
                        autocompleteValue: {
                          $ref: "#/definitions/integrationAutocomplete",
                          title: "Value",
                        },
                      },
                      required: ["autocompleteValue"],
                    },
                    else: {
                      allOf: [
                        {
                          if: {
                            properties: {
                              name: {
                                anyOf: [
                                  { const: "summary" },
                                  { const: "description" },
                                ],
                              },
                            },
                          },
                          then: {
                            properties: {
                              value: {
                                $ref: "#/definitions/expression",
                              },
                            },
                          },
                          required: ["name", "value"],
                        },
                      ],
                    },
                  },
                ],
              },
            },
          },
          required: ["fields"],
        },
        uiSchema: {
          fields: {
            items: {
              value: {
                "ui:widget": "ExpressionWidget",
              },
              autocompleteValue: {
                "ui:widget": "IntegrationAutocompleteWidget",
                "ui:options": {
                  slug: "listObjectFieldValues",
                  params: {
                    fieldName: "$this.$parent.name",
                  },
                  allowExpression: true,
                  allowRefresh: true,
                },
              },
            },
          },
        },
      },
    },
    search: {
      name: "Search",
      description: "Retrieve an issue from JIRA",
      config: {
        jsonSchema: {
          type: "object",
          properties: {
            issueIdOrKey: { type: "string", title: "Issue ID or Key" },
          },
          required: ["issueIdOrKey"],
        },
        uiSchema: {},
      },
    },
  },
};
