import type { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types.js";

import type { ConnectionTypes } from "../../../../index.js";

const shouldUseRawField = (fieldName: string): boolean => {
  const rawFields = [
    // Simple fields
    "all_time_title",
    "current_company_location",
    "current_company_name",
    "current_location",
    "current_title",
    "full_name",
    "location_continent",
    "location_locality",
    "location_region",
    "recent_title",
    "skills",
    // Education fields (nested)
    "education.school.name",
    "education.school.location.name",
    // Experience fields (nested)
    "experience.company.name",
    "experience.company.location.name",
    "experience.company.location.continent",
    "experience.title.name",
  ];

  return rawFields.includes(fieldName);
};

const flattenBool = (
  items: QueryDslQueryContainer[],
  mode: "filter" | "should",
) => {
  if (items.length === 1) {
    return items[0] as QueryDslQueryContainer;
  }
  return { bool: { [mode]: items } };
};

export const reshapeIntegrationFilterToSwarmQuery = (
  filter: ConnectionTypes.IntegrationFilter,
): QueryDslQueryContainer => {
  const dslGroups = filter.groups.map<QueryDslQueryContainer>((group) => {
    const dslConditions = group.conditions.map<QueryDslQueryContainer>(
      (condition) => {
        const fieldBase = `profile_info.${condition.propertyName}`;

        if (condition.operator === "is") {
          const values = Array.isArray(condition.values)
            ? condition.values
            : [condition.values];

          if (values.length === 1) {
            if (shouldUseRawField(condition.propertyName)) {
              return {
                terms: { [`${fieldBase}.raw`]: values },
              };
            }
            return {
              match: {
                [fieldBase]: {
                  query: values[0],
                },
              },
            };
          } else {
            if (shouldUseRawField(condition.propertyName)) {
              return {
                terms: { [`${fieldBase}.raw`]: values },
              };
            }
            return {
              terms: { [fieldBase]: values },
            };
          }
        }

        if (condition.operator === "isNot") {
          const values = Array.isArray(condition.values)
            ? condition.values
            : [condition.values];

          if (shouldUseRawField(condition.propertyName)) {
            return {
              bool: {
                must_not: [
                  {
                    terms: { [`${fieldBase}.raw`]: values },
                  },
                ],
              },
            };
          }
          return {
            bool: {
              must_not: [
                {
                  terms: { [fieldBase]: values },
                },
              ],
            },
          };
        }

        if (condition.operator === "contains") {
          const values = Array.isArray(condition.values)
            ? condition.values
            : [condition.values];

          if (values.length === 1) {
            return {
              match: {
                [fieldBase]: {
                  query: values[0],
                },
              },
            };
          }
          return {
            bool: {
              should: values.map((value) => ({
                match: {
                  [fieldBase]: { query: value },
                },
              })),
            },
          };
        }

        if (condition.operator === "notContains") {
          const values = Array.isArray(condition.values)
            ? condition.values
            : [condition.values];

          return {
            bool: {
              must_not: values.map((value) => ({
                match: {
                  [fieldBase]: { query: value },
                },
              })),
            },
          };
        }

        if (condition.operator === "between") {
          return {
            range: {
              [fieldBase]: {
                gte: condition.value,
                lte: condition.otherValue,
              },
            },
          };
        }
        if (condition.operator === "greaterThan") {
          return {
            range: {
              [fieldBase]: {
                gt: condition.value,
              },
            },
          };
        }
        if (condition.operator === "greaterThanOrEquals") {
          return {
            range: {
              [fieldBase]: {
                gte: condition.value,
              },
            },
          };
        }
        if (condition.operator === "lowerThan") {
          return {
            range: {
              [fieldBase]: {
                lt: condition.value,
              },
            },
          };
        }
        if (condition.operator === "lowerThanOrEquals") {
          return {
            range: {
              [fieldBase]: {
                lte: condition.value,
              },
            },
          };
        }
        if (condition.operator === "isNotNull") {
          return {
            exists: {
              field: fieldBase,
            },
          };
        }
        if (condition.operator === "isNull") {
          return {
            bool: {
              must_not: [
                {
                  exists: {
                    field: fieldBase,
                  },
                },
              ],
            },
          };
        }

        throw new Error("Invalid condition");
      },
    );

    return flattenBool(
      dslConditions,
      group.conjonction === "and" ? "filter" : "should",
    );
  });

  return flattenBool(
    dslGroups,
    filter.conjonction === "and" ? "filter" : "should",
  );
};
