import type { ConnectionTypes } from "../../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../../dependencies.js";
import { buildSwarmClient } from "../client.js";
import { profileColumns } from "../schemas/profile.js";
import type {
  ConnectorConfig,
  ModelConfig,
  ModelMeta,
  ModelSlug,
} from "../swarm.js";
import { reshapeIntegrationFilterToSwarmQuery } from "../utils.js";

const LIMIT_PER_CALL = 60;
const DEFAULT_MODEL_LIMIT = 100;

export type ModelFetchPayload = ConnectionTypes.IntegrationModelFetchPayload<
  ConnectorConfig,
  ModelSlug,
  ModelConfig,
  ModelMeta
>;

export const modelFetch = async (
  _dependencies: ConnectionServicesDependencies,
  payload: ModelFetchPayload,
): Promise<ConnectionTypes.IntegrationModelFetchResult<ModelMeta>> => {
  const { connector, config, meta } = payload;
  const { filter, mappingCompanyWebsite } = config;

  const swarmClient = buildSwarmClient({
    apiKey: connector.config.apiKey,
  });

  const offset = meta !== undefined ? meta.offset : 0;
  const size = Math.min(LIMIT_PER_CALL, DEFAULT_MODEL_LIMIT - offset);

  if (size <= 0) {
    return {
      outcome: "fetched",
      count: 0,
      columns: profileColumns,
      idColumnSlug: "id",
      titleColumnSlug: "full_name",
      data: {
        kind: "records",
        records: [],
        hasMore: false,
      },
    };
  }

  const query = reshapeIntegrationFilterToSwarmQuery(filter);

  const { items: profiles, total_count } =
    await swarmClient.searchNetworkMapper({
      query,
      mapping_company_website: mappingCompanyWebsite,
    });

  const hasMore =
    offset + profiles.length < Math.min(total_count, DEFAULT_MODEL_LIMIT);
  const nextOffset = hasMore === true ? offset + profiles.length : undefined;

  const reshapedProfiles = profiles.map((profile) => {
    const { profile: profileData, connections, ...rest } = profile;

    return {
      ...profileData,
      connections_count: connections.length,
      ...rest,
    };
  });

  return {
    outcome: "fetched",
    count: reshapedProfiles.length,
    columns: profileColumns,
    idColumnSlug: "id",
    titleColumnSlug: "full_name",
    data: {
      kind: "records",
      records: reshapedProfiles.map((profile) => {
        return {
          action: "upsert",
          override: true,
          record: profile,
        };
      }),
      hasMore,
      meta: nextOffset !== undefined ? { offset: nextOffset } : undefined,
    },
  };
};
