import type { ConnectionTypes } from "../../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../../dependencies.js";
import { buildJiraClient } from "../client.js";
import type { ConnectorConfig, WriteActionConfig } from "../jira.js";
import { reshapedFields } from "../utils.js";

export type WriteActionPayload =
  ConnectionTypes.IntegrationActionExecutePayload<
    ConnectorConfig,
    WriteActionConfig
  >;

export type WriteActionResult = ConnectionTypes.IntegrationActionExecuteResult;

export const writeAction = async (
  _dependencies: ConnectionServicesDependencies,
  payload: WriteActionPayload,
): Promise<WriteActionResult> => {
  const { connector, config } = payload;

  const jiraClient = buildJiraClient({
    apiKey: connector.config.apiKey,
    domain: connector.config.domain,
    userEmail: connector.config.userEmail,
  });

  const { fields } = config;

  const result = await jiraClient.createIssue(reshapedFields(fields));

  return {
    outcome: "executed",
    title: `✅ Issue created: ${result.key}`,
    data: result,
  };
};
