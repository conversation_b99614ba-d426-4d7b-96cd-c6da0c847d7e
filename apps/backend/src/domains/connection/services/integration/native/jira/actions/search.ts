import type { ConnectionTypes } from "../../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../../dependencies.js";
import { buildJiraClient } from "../client.js";
import type { ConnectorConfig, SearchActionConfig } from "../jira.js";

export type SearchActionPayload =
  ConnectionTypes.IntegrationActionExecutePayload<
    ConnectorConfig,
    SearchActionConfig
  >;

export type SearchActionResult = ConnectionTypes.IntegrationActionExecuteResult;

export const searchAction = async (
  _dependencies: ConnectionServicesDependencies,
  payload: SearchActionPayload,
): Promise<SearchActionResult> => {
  const { connector, config } = payload;

  const jiraClient = buildJiraClient({
    apiKey: connector.config.apiKey,
    domain: connector.config.domain,
    userEmail: connector.config.userEmail,
  });

  const { issueIdOrKey } = config;

  const result = await jiraClient.getIssue(issueIdOrKey);

  return {
    outcome: "executed",
    title: `✅ Issue retrieved: ${result.key}`,
    data: result,
  };
};
