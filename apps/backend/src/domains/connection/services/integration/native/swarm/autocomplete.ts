import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { profileColumns } from "./schemas/profile.js";
import type { ConnectorConfig } from "./swarm.js";

export type AutocompletePayload =
  ConnectionTypes.IntegrationAutocompletePayload<
    ConnectorConfig,
    "listObjectProperties",
    { objectType: "profile" }
  >;

export const autocomplete = async (
  _dependencies: ConnectionServicesDependencies,
  payload: AutocompletePayload,
): Promise<ConnectionTypes.IntegrationAutocompleteResult> => {
  const { params } = payload;
  const { objectType } = params;

  if (objectType === "profile") {
    return {
      results: profileColumns.map((column) => ({
        label: column.label,
        value: column.slug,
        parent: undefined,
      })),
    };
  }

  return {
    results: [],
  };
};
