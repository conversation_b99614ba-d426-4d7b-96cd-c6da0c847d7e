import type { AxiosError } from "axios";
import axios from "axios";
import { getEntries, getKeys } from "cargo-utils";
import { stringifyJson } from "cargo-utils";

export type LushaDependencies = {
  apiKey: string;
};

export type GetPersonFilterPayload = {
  fields: {
    name:
      | "companyName"
      | "companyDomain"
      | "firstName"
      | "lastName"
      | "email"
      | "linkedinUrl";
    value?: string;
  }[];
  filterBy: "phoneNumbers" | "emailAddresses" | "none";
  refreshJobInfo?: boolean;
};

type CompanySicCode = {
  code: string;
  label: string;
};

type CompanyNaicCode = {
  code: string;
  label: string;
};

export type GetPersonFilterResponse = {
  contact: {
    error: unknown | null;
    isCreditCharged: boolean;
    data: Record<string, unknown>;
  };
};

export type GetPeopleBulkPayload = {
  contactId: string;
  fullName: string;
  email?: string;
  linkedinUrl?: string;
  location?: string;
  companies: { domain: string; name: string; isCurrent: boolean }[];
}[];

export type GetCompanyFilterPayload = {
  name: "company" | "domain";
  value?: string;
}[];

export type FilterLocation = {
  continent: string;
  country: string;
  city: string;
  state: string;
  countryGrouping: string;
};

type ContactFiltersList = {
  departments?: string[];
  seniority?: string[];
  existingDataPoints?: string[];
  locations?: FilterLocation[];
};

type CompanyFiltersList = {
  names?: string[];
  locations?: FilterLocation[];
  technologies?: string[];
  mainIndustriesIds?: string[];
  subIndustriesIds?: string[];
  intentTopics?: string[];
  sizes?: { min?: number; max?: number }[];
  revenues?: { min?: number; max?: number }[];
  sics?: string[];
  naics?: string[];
};

export type SearchContactsFilter = {
  contacts: {
    include: ContactFiltersList;
    exclude: ContactFiltersList;
  };
  companies: {
    include: CompanyFiltersList;
    exclude: CompanyFiltersList;
  };
};

export type SearchCompaniesFilter = {
  companies: {
    include: CompanyFiltersList;
    exclude: CompanyFiltersList;
  };
};

type SearchContactsResponse = {
  requestId: string;
  currentPage: number;
  totalResults: number;
  data: {
    contactId: string;
    isShown: boolean;
    name: {
      raw: string;
      full: string;
      first: string;
      last: string;
    };
    jobTitle: string;
    companyId: number;
    companyName: string;
    fqdn: string;
    companyDescription: string;
    logoUrl: string;
    hasCompanyEmployeesCount: boolean;
    hasCompanyRevenue: boolean;
    hasCompanyMainIndustry: boolean;
    hasCompanySubIndustry: boolean;
    hasCompanyFunding: boolean;
    hasCompanyIntent: boolean;
    hasCompanyTechnologies: boolean;
    hasDepartment: boolean;
    hasSeniority: boolean;
    hasContactLocation: boolean;
    hasSocialLink: boolean;
    hasEmails: boolean;
    hasWorkEmail: boolean;
    hasPrivateEmail: boolean;
    hasPhones: boolean;
    hasMobilePhone: boolean;
    hasDirectPhone: boolean;
    hasCompanyCity: boolean;
    hasCompanyCountry: boolean;
  }[];
};

type SearchCompaniesResponse = {
  requestId: string;
  currentPage: number;
  pageLength: number;
  totalResults: number;
  data?: {
    id: number;
    name: string;
    fqdn: string;
    description: string;
    logoUrl: string;
    hasCompanyEmployeesCount: boolean;
    hasCompanyRevenue: boolean;
    hasCompanyMainIndustry: boolean;
    hasCompanySubIndustry: boolean;
    hasCompanyFunding: boolean;
    hasCompanyIntent: boolean;
    hasCompanyTechnologies: boolean;
    hasCompanyCity: boolean;
    hasCompanyCountry: boolean;
  }[];
};

export type LushaClient = {
  isAuthenticated: () => Promise<boolean>;
  getPerson: (
    filter: GetPersonFilterPayload,
  ) => Promise<GetPersonFilterResponse | undefined>;
  getCompany: (
    filter: GetCompanyFilterPayload,
  ) => Promise<Record<string, unknown> | undefined>;
  searchCompanies: (
    filters: SearchCompaniesFilter,
    limit: number,
  ) => Promise<SearchCompaniesResponse>;
  searchContacts: (
    filters: SearchContactsFilter,
    limit: number,
  ) => Promise<SearchContactsResponse>;
  getPeopleBulk: (
    payload: GetPeopleBulkPayload,
  ) => Promise<Record<string, unknown>[]>;
  getCompaniesBulk: (ids: string[]) => Promise<Record<string, unknown>[]>;
  getFilterCompaniesSizes: () => Promise<
    {
      min?: number;
      max?: number;
    }[]
  >;
  getFilterCompaniesRevenues: () => Promise<
    {
      min?: number;
      max?: number;
    }[]
  >;
  getFilterCompaniesLocations: () => Promise<
    {
      id: string;
      continent?: string;
      country?: string;
      country_grouping?: string;
      state?: string;
      city?: string;
      key: string;
    }[]
  >;
  getFilterContactDepartments: () => Promise<string[]>;
  getFilterContactSeniority: () => Promise<
    {
      id: number;
      name: string;
    }[]
  >;
  getFilterContactAllCountries: () => Promise<
    {
      name: string;
      code: string;
    }[]
  >;
  getFilterContactExistingDataPoints: () => Promise<string[]>;
  getFilterCompaniesNaicsCodes: () => Promise<CompanyNaicCode[]>;
  getFilterCompaniesSicCodes: () => Promise<CompanySicCode[]>;
};

const BASE_URL = "https://api.lusha.com";

export const buildLushaClient = (
  dependencies: LushaDependencies,
): LushaClient => {
  const { apiKey } = dependencies;

  const client = axios.create({
    baseURL: BASE_URL,
    headers: {
      "content-type": "application/json",
      api_key: apiKey,
    },
  });

  const isAuthenticated = async (): Promise<boolean> => {
    try {
      await client.get("/");
      return true;
    } catch (error) {
      const axiosError = error as AxiosError;
      if (
        axiosError.response !== undefined &&
        axiosError.response.status === 401
      ) {
        return false;
      }

      return true;
    }
  };

  const getPerson = async (
    filter: GetPersonFilterPayload,
  ): Promise<GetPersonFilterResponse | undefined> => {
    const fieldFilters: Record<string, unknown> = {};
    filter.fields.forEach((field) => {
      fieldFilters[field.name] = field.value;
    });

    try {
      const { data } = await client.get<GetPersonFilterResponse>("/v2/person", {
        params: {
          ...fieldFilters,
          filterBy: filter.filterBy,
          refreshJobInfo: filter.refreshJobInfo,
        },
      });

      if (
        typeof data.contact.error === "object" &&
        data.contact.error !== null
      ) {
        return undefined;
      }

      return data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const getCompany = async (
    filter: GetCompanyFilterPayload,
  ): Promise<Record<string, unknown> | undefined> => {
    const params: Record<string, unknown> = {};
    filter.forEach((field) => {
      params[field.name] = field.value;
    });

    try {
      const { data } = await client.get("/company", {
        params,
      });

      if (typeof data.error === "object" && data.error !== null) {
        return undefined;
      }

      return data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const getFilterCompaniesSizes = async (): Promise<
    { min?: number; max?: number }[]
  > => {
    try {
      const response = await client.get(`/prospecting/filters/companies/sizes`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterCompaniesRevenues = async (): Promise<
    { min?: number; max?: number }[]
  > => {
    try {
      const response = await client.get(
        `/prospecting/filters/companies/revenues`,
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterCompaniesLocations = async (): Promise<
    {
      id: string;
      continent?: string;
      country?: string;
      country_grouping?: string;
      state?: string;
      city?: string;
      key: string;
    }[]
  > => {
    try {
      const response = await client.post(
        `/prospecting/filters/companies/locations`,
        {
          text: "", // FIXME: Text param is required, should adapt to what the user is typing
        },
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterContactDepartments = async (): Promise<string[]> => {
    try {
      const response = await client.get(
        `/prospecting/filters/contacts/departments`,
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterContactSeniority = async (): Promise<
    { id: number; name: string }[]
  > => {
    try {
      const response = await client.get(
        `/prospecting/filters/contacts/seniority`,
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterContactAllCountries = async (): Promise<
    { name: string; code: string }[]
  > => {
    try {
      const response = await client.get(
        `/prospecting/filters/contacts/all_countries`,
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterContactExistingDataPoints = async (): Promise<string[]> => {
    try {
      const response = await client.get(
        `/prospecting/filters/contacts/existing_data_points`,
      );
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const searchContacts = async (
    filters: SearchContactsFilter,
    limit: number,
  ): Promise<SearchContactsResponse> => {
    try {
      const response = await client.post("/prospecting/contact/search", {
        filters: {
          companies: {
            include:
              getKeys(filters.companies.include).length > 0
                ? filters.companies.include
                : undefined,
            exclude:
              getKeys(filters.companies.exclude).length > 0
                ? filters.companies.exclude
                : undefined,
          },
          contacts: {
            include:
              getKeys(filters.contacts.include).length > 0
                ? filters.contacts.include
                : undefined,
            exclude:
              getKeys(filters.contacts.exclude).length > 0
                ? filters.contacts.exclude
                : undefined,
          },
        },
        offset: {
          index: 0,
          size: limit,
        },
      });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const searchCompanies = async (
    filters: SearchCompaniesFilter,
    limit: number,
  ): Promise<SearchCompaniesResponse> => {
    try {
      const response = await client.post("/prospecting/company/search", {
        filters: {
          companies: {
            include:
              getKeys(filters.companies.include).length > 0
                ? filters.companies.include
                : undefined,
            exclude:
              getKeys(filters.companies.exclude).length > 0
                ? filters.companies.exclude
                : undefined,
          },
        },
        offset: {
          index: 0,
          size: limit,
        },
      });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const getPeopleBulk = async (
    contacts: GetPeopleBulkPayload,
  ): Promise<Record<string, unknown>[]> => {
    try {
      const response = await client.post("/v2/person", {
        contacts,
      });

      return getEntries(response.data.contacts).map(([contactId, contact]) => {
        return {
          contactId,
          ...contact,
        };
      });
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const getCompaniesBulk = async (
    ids: string[],
  ): Promise<Record<string, unknown>[]> => {
    try {
      const response = await client.post("/bulk/company", {
        companies: ids.map((id) => {
          return id;
        }),
      });

      return getEntries(response.data.companies).map(([id, company]) => {
        return {
          id,
          ...company,
        };
      });
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  const getFilterCompaniesNaicsCodes = async (): Promise<CompanyNaicCode[]> => {
    try {
      const response = await client.get(`/prospecting/filters/companies/naics`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };
  const getFilterCompaniesSicCodes = async (): Promise<CompanySicCode[]> => {
    try {
      const response = await client.get(`/prospecting/filters/companies/sics`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<Record<string, unknown>>;

      if (
        axiosError.response !== undefined &&
        typeof axiosError.response.data === "object"
      ) {
        throw new Error(
          stringifyJson(
            axiosError.response?.data["message"] || axiosError.response?.data,
          ),
        );
      }

      throw error;
    }
  };

  return {
    isAuthenticated,
    getPerson,
    getCompany,
    searchCompanies,
    searchContacts,
    getPeopleBulk,
    getCompaniesBulk,
    getFilterCompaniesLocations,
    getFilterCompaniesRevenues,
    getFilterCompaniesSizes,
    getFilterContactAllCountries,
    getFilterContactDepartments,
    getFilterContactSeniority,
    getFilterContactExistingDataPoints,
    getFilterCompaniesNaicsCodes,
    getFilterCompaniesSicCodes,
  };
};
