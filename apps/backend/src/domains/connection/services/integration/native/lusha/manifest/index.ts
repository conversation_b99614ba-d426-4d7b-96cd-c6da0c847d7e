import type { Schema as JsonSchema } from "jsonschema";

import type { ConnectionTypes } from "../../../../../index.js";
import {
  DEFAULT_SEARCH_ACTION_LIMIT,
  MAX_SEARCH_ACTION_LIMIT,
  MIN_SEARCH_ACTION_LIMIT,
} from "../lusha.js";
import { companySchema } from "./company.js";
import { contactSchema } from "./contact.js";
import { icon } from "./icon.js";

const searchUiSchema: Record<string, unknown> = {
  limit: { "ui:options": { suffix: "records" } },
  fields: {
    items: {
      autocompleteValues: {
        "ui:widget": "IntegrationAutocompleteWidget",
        "ui:options": {
          slug: "listObjectFieldValues",
          allowRefresh: true,

          params: {
            fieldName: "$this.$parent.name",
            objectType: "$this.$parent.$parent.$parent.objectType",
          },
        },
      },
      values: {
        "ui:widget": "IntegrationAutocompleteWidget",
        "ui:options": {
          allowExpression: true,
          multiple: true,
        },
      },
    },
  },
  companyFields: {
    items: {
      autocompleteValues: {
        "ui:widget": "IntegrationAutocompleteWidget",
        "ui:options": {
          slug: "listObjectFieldValues",
          allowRefresh: true,
          params: {
            fieldName: "$this.$parent.name",
            objectType: "company",
          },
        },
      },
      values: {
        "ui:widget": "IntegrationAutocompleteWidget",
        "ui:options": {
          allowExpression: true,
          multiple: true,
        },
      },
    },
  },
};

const searchJsonSchema: JsonSchema = {
  type: "object",
  properties: {
    objectType: {
      title: "Object type",
      type: "string",
      oneOf: [
        {
          const: "contact",
          title: "Contact",
        },
        {
          const: "company",
          title: "Company",
        },
      ],
      default: "contact",
    },
  },
  allOf: [
    {
      if: {
        properties: {
          objectType: {
            const: "contact",
          },
        },
      },
      then: {
        type: "object",
        properties: {
          fields: contactSchema.properties!["fields"] as JsonSchema,
          companyFields: {
            ...companySchema.properties!["fields"],
            title: "Company fields",
          },
        },
      },
    },
    {
      if: {
        properties: {
          objectType: {
            const: "company",
          },
        },
      },
      then: {
        type: "object",
        properties: {
          fields: {
            ...companySchema.properties!["fields"],
            title: "Company fields",
          },
        },
      },
    },
    {
      properties: {
        limit: {
          description: `Maximum number of records to retrieve. ${MIN_SEARCH_ACTION_LIMIT} is the minimum. Defaults to ${DEFAULT_SEARCH_ACTION_LIMIT} with an upper limit of ${MAX_SEARCH_ACTION_LIMIT} records.`,
          type: "number",
          default: DEFAULT_SEARCH_ACTION_LIMIT,
          minimum: MIN_SEARCH_ACTION_LIMIT,
          maximum: MAX_SEARCH_ACTION_LIMIT,
          title: "Limit",
        },
      },
    },
  ],
  required: ["objectType"],
};

export const manifest: ConnectionTypes.IntegrationManifest = {
  name: "Lusha",
  slug: "lusha",
  url: "https://lusha.com",
  description: "Lusha integration",
  icon,
  color: "#000",
  category: "enrichment",
  autocompletes: [
    {
      slug: "listObjectFieldValues",
      params: {
        jsonSchema: {
          type: "object",
          properties: {
            fieldName: { type: "string" },
            objectType: { type: "string" },
          },
          required: ["fieldName", "objectType"],
        },
      },
    },
  ],
  dynamicSchemas: [],
  connector: {
    rateLimit: {
      unit: "second",
      max: 50,
    },
    config: {
      jsonSchema: {
        type: "object",
        properties: {
          apiKey: {
            title: "API Key",
            $ref: "#/definitions/encryption",
          },
        },
        required: ["apiKey"],
      },
      uiSchema: {
        apiKey: {
          "ui:widget": "EncryptionWidget",
        },
      },
    },
    credits: {
      isLegacyCompatible: true,
    },
    caching: {
      isCompatible: true,
    },
  },
  actions: {
    search: {
      name: "Search",
      description: "Retrieve object records",
      config: {
        jsonSchema: searchJsonSchema,
        uiSchema: searchUiSchema,
      },
      credits: {
        costs: [
          {
            config: {
              jsonSchema: {
                type: "object",
                properties: {
                  enrich: {
                    const: true,
                  },
                },
                required: ["enrich"],
              },
            },
            cost: 3,
            type: "unit",
            unit: "item",
          },
          {
            config: {
              jsonSchema: {
                type: "object",
                properties: {
                  enrich: {
                    const: false,
                  },
                },
              },
            },
            cost: 3,
            type: "fixed",
          },
        ],
      },
    },
    enrich: {
      name: "Enrich",
      description: "Retrieve information about a person/company",
      config: {
        jsonSchema: {
          type: "object",
          properties: {
            objectType: {
              title: "Object type",
              type: "string",
              oneOf: [
                {
                  const: "person",
                  title: "Person",
                },
                {
                  const: "company",
                  title: "Company",
                },
              ],
              default: "person",
            },
          },
          allOf: [
            {
              if: {
                properties: {
                  objectType: {
                    const: "person",
                  },
                },
              },
              then: {
                properties: {
                  fields: {
                    title: "Fields",
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        name: {
                          title: "Name",
                          type: "string",
                          oneOf: [
                            {
                              const: "firstName",
                              title: "First name",
                            },
                            {
                              const: "lastName",
                              title: "Last name",
                            },
                            {
                              const: "email",
                              title: "Email",
                            },
                            {
                              const: "companyName",
                              title: "Company name",
                            },
                            {
                              const: "companyDomain",
                              title: "Company domain",
                            },
                            {
                              const: "linkedinUrl",
                              title: "Linkedin URL",
                            },
                          ],
                          default: "firstName",
                        },
                        value: {
                          $ref: "#/definitions/expression",
                          title: "Value",
                        },
                      },
                      required: ["name", "value"],
                    },
                    description:
                      "Provide at least a first name, last name and a company name or domain",
                  },
                  filterBy: {
                    title: "Filter by",
                    type: "string",
                    oneOf: [
                      {
                        const: "emailAddresses",
                        title: "Email",
                      },
                      {
                        const: "phoneNumbers",
                        title: "Phone",
                      },
                    ],
                    default: "emailAddresses",
                  },
                  refreshJobInfo: {
                    title: "Refresh Job",
                    type: "boolean",
                    description:
                      "Use Refresh Job Info for Real-Time Job Updates",
                    default: false,
                  },
                },
                required: ["fields", "filterBy"],
              },
            },
            {
              if: {
                properties: {
                  objectType: {
                    const: "company",
                  },
                },
              },
              then: {
                properties: {
                  fields: {
                    title: "Fields",
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        name: {
                          title: "Name",
                          type: "string",
                          oneOf: [
                            {
                              const: "company",
                              title: "Company",
                            },
                            {
                              const: "domain",
                              title: "Domain",
                            },
                          ],
                          default: "company",
                        },
                        value: {
                          $ref: "#/definitions/expression",
                          title: "Value",
                        },
                      },
                      required: ["name", "value"],
                    },
                    description: "Provide at least a domain or a company",
                  },
                },
                required: ["fields"],
              },
            },
          ],
          required: ["objectType"],
        },
        uiSchema: {
          fields: {
            items: {
              value: {
                "ui:widget": "ExpressionWidget",
              },
            },
          },
          filterBy: {
            "ui:widget": "RadioWidget",
          },
        },
      },
      credits: {
        costs: [
          {
            config: { jsonSchema: { type: "object" } },
            cost: 3,
            type: "fixed",
          },
        ],
      },
    },
  },
  users: {
    config: {
      jsonSchema: {},
      uiSchema: {},
    },
  },
};
