import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { buildLushaClient } from "./client.js";
import type { ConnectorConfig } from "./lusha.js";

export type AutocompletePayload =
  ConnectionTypes.IntegrationAutocompletePayload<
    ConnectorConfig,
    "listObjectFieldValues",
    { fieldName: string; objectType: "contact" | "company" }
  >;

export type AutocompleteResult = ConnectionTypes.IntegrationAutocompleteResult;

export const autocomplete = async (
  dependencies: ConnectionServicesDependencies,
  payload: AutocompletePayload,
): Promise<AutocompleteResult> => {
  const { credits } = dependencies;
  const { connector } = payload;

  const lushaClient = buildLushaClient({
    apiKey:
      connector.config === null
        ? credits.lusha.apiKey
        : connector.config.apiKey,
  });

  if (payload.slug === "listObjectFieldValues") {
    if (payload.params.objectType === "company") {
      if (payload.params.fieldName === "sizes") {
        const sizes = await lushaClient.getFilterCompaniesSizes();

        return {
          results: sizes.map((size) => ({
            label:
              size.max !== undefined
                ? `${size.min} - ${size.max}`
                : `${size.min}+`,
            value: JSON.stringify(size),
          })),
        };
      } else if (payload.params.fieldName === "revenues") {
        const revenues = await lushaClient.getFilterCompaniesRevenues();

        return {
          results: revenues.map((revenue) => ({
            label:
              revenue.max !== undefined
                ? `${revenue.min} - ${revenue.max}`
                : `${revenue.min}+`,
            value: JSON.stringify(revenue),
          })),
        };
      } else if (payload.params.fieldName === "locations") {
        // TODO: JSON.stringify to test, but make it better for label
        const locations = await lushaClient.getFilterCompaniesLocations();

        return {
          results: locations.map((location) => {
            if (location.key === "country") {
              return {
                label: location.country!,
                value: JSON.stringify(location),
                parent: "Country",
              };
            }

            if (location.key === "continent") {
              return {
                label: location.continent!,
                value: JSON.stringify(location),
                parent: "Continent",
              };
            }

            if (location.key === "city") {
              return {
                label: `${location.country} - ${location.city}`,
                value: JSON.stringify(location),
                parent: "City",
              };
            }

            if (location.key === "state") {
              return {
                label: `${location.country} - ${location.state}`,
                value: location.id,
                parent: "State",
              };
            }

            return {
              label: location.country_grouping!,
              value: location.id,
            };
          }),
        };
      } else if (payload.params.fieldName === "naics") {
        const naicsCodes = await lushaClient.getFilterCompaniesNaicsCodes();
        return {
          results: naicsCodes.map((naic) => ({
            label: naic.label,
            value: naic.code,
          })),
        };
      } else if (payload.params.fieldName === "sics") {
        const sicCodes = await lushaClient.getFilterCompaniesSicCodes();
        return {
          results: sicCodes.map((sic) => ({
            label: sic.label,
            value: sic.code,
          })),
        };
      }
    }

    if (payload.params.objectType === "contact") {
      if (payload.params.fieldName === "countries") {
        const countries = await lushaClient.getFilterContactAllCountries();

        return {
          results: countries.map(({ name, code }) => ({
            label: name,
            value: code,
          })),
        };
      } else if (payload.params.fieldName === "seniority") {
        const seniorities = await lushaClient.getFilterContactSeniority();

        return {
          results: seniorities.map(({ id, name }) => ({
            value: `${id}`,
            label: name,
          })),
        };
      } else if (payload.params.fieldName === "departments") {
        const departments = await lushaClient.getFilterContactDepartments();

        return {
          results: departments.map((department) => ({
            label: department,
            value: department,
          })),
        };
      } else if (payload.params.fieldName === "existingDataPoints") {
        const existingDataPoints =
          await lushaClient.getFilterContactExistingDataPoints();

        return {
          results: existingDataPoints.map((existingDataPoint) => ({
            label: existingDataPoint,
            value: existingDataPoint,
          })),
        };
      } else if (payload.params.fieldName === "locations") {
        // TODO: JSON.stringify to test, but make it better for label
        const locations = await lushaClient.getFilterCompaniesLocations();

        return {
          results: locations.map((location) => {
            if (location.key === "country") {
              return {
                label: location.country!,
                value: JSON.stringify(location),
                parent: "Country",
              };
            }

            if (location.key === "continent") {
              return {
                label: location.continent!,
                value: JSON.stringify(location),
                parent: "Continent",
              };
            }

            if (location.key === "city") {
              return {
                label: `${location.country} - ${location.city}`,
                value: JSON.stringify(location),
                parent: "City",
              };
            }

            if (location.key === "state") {
              return {
                label: `${location.country} - ${location.state}`,
                value: location.id,
                parent: "State",
              };
            }

            return {
              label: location.country_grouping!,
              value: location.id,
            };
          }),
        };
      }
    }
  }

  return { results: [] };
};
