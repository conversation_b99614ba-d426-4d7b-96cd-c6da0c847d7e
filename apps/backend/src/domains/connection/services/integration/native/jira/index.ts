import path from "path";
import { fileURLToPath } from "url";

import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { validateAction } from "../../utils.js";
import { searchAction } from "./actions/search.js";
import { writeAction } from "./actions/write.js";
import { authenticate } from "./authenticate.js";
import { autocomplete } from "./autocomplete.js";
import { zodSearchActionConfig, zodWriteActionConfig } from "./jira.js";
import { manifest } from "./manifest.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const buildJiraIntegration = (
  dependencies: ConnectionServicesDependencies,
): ConnectionTypes.Integration => {
  return {
    documentationPath: path.join(__dirname, "documentation"),
    manifest,
    actions: {
      write: {
        execute: (payload) => writeAction(dependencies, payload),
        validate: async (payload) =>
          validateAction(payload.config, zodWriteActionConfig),
      },
      search: {
        execute: (payload) => searchAction(dependencies, payload),
        validate: async (payload) =>
          validateAction(payload.config, zodSearchActionConfig),
      },
    },
    autocomplete: async (payload) => autocomplete(dependencies, payload),
    authenticate: async (payload) => authenticate(dependencies, payload),
  };
};
