import type { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types.js";
import type { AxiosError } from "axios";
import axios from "axios";
import { stringifyJson } from "cargo-utils";

export type SwarmClientDependencies = {
  apiKey: string;
};

export type SwarmNetworkMapperPayload = {
  query: QueryDslQueryContainer;
  mapping_company_website?: string;
};

type Connection = {
  connector_id: string;
  connector_name: string;
  connector_linkedin_url: string;
  connector_current_title: string;
  connector_current_company_name: string;
  connection_strength_normalized: number;
  connection_strength: number;
  manual_strength: number;
  sources: Source[];
};

type Source = {
  origin: string;
  shared_company: string;
  shared_company_id: string;
  shared_company_website: string;
  shared_company_linkedin_slug: string;
  overlap_start_date: string;
  overlap_end_date: string;
  overlap_duration_months: string;
  shared_school: string;
  shared_major: string;
  graduation_year: string;
  investor: string;
  portfolio_company: string;
};

type NetworkMapperItem = {
  profile: {
    id: string;
    full_name: string;
    current_title: string;
    linkedin_url: string;
    work_email: string;
    current_company_name: string;
    current_company_website: string;
  };
  connections: Connection[];
};

export type SwarmNetworkMapperResponse = {
  items: NetworkMapperItem[];
  count: number;
  total_count: number;
};

export type SwarmSearchProfilesPayload = {
  query: QueryDslQueryContainer;
  limit?: number;
};

export type SwarmSearchProfilesResponse = {
  ids: string[];
  totalCount: number;
  paginationToken: string;
};

export type SwarmClient = {
  isAuthenticated: () => Promise<boolean>;
  searchNetworkMapper: (
    payload: SwarmNetworkMapperPayload,
  ) => Promise<SwarmNetworkMapperResponse>;
  searchProfiles: (
    payload: SwarmSearchProfilesPayload,
  ) => Promise<SwarmSearchProfilesResponse>;
};

const BASE_URL = "https://bee.theswarm.com/v2";

export const buildSwarmClient = (
  dependencies: SwarmClientDependencies,
): SwarmClient => {
  const { apiKey } = dependencies;

  const client = axios.create({
    baseURL: BASE_URL,
    headers: {
      "Content-Type": "application/json",
      "x-api-key": apiKey,
    },
  });

  return {
    isAuthenticated: async () => {
      try {
        await client.get("/");
        return true;
      } catch (error) {
        const axiosError = error as AxiosError;
        if (
          axiosError.response !== undefined &&
          axiosError.response.status === 401
        ) {
          return false;
        }

        return true;
      }
    },
    searchNetworkMapper: async (payload) => {
      try {
        const { data } = await client.post("/profiles/network-mapper", payload);
        return data;
      } catch (error) {
        const axiosError = error as AxiosError;

        if (axiosError.response !== undefined) {
          throw new Error(stringifyJson(axiosError.response.data));
        }

        throw error;
      }
    },
    searchProfiles: async (payload) => {
      try {
        const { data } = await client.post("/profiles/search", payload);
        return data;
      } catch (error) {
        const axiosError = error as AxiosError;

        if (axiosError.response !== undefined) {
          throw new Error(stringifyJson(axiosError.response.data));
        }

        throw error;
      }
    },
  };
};
