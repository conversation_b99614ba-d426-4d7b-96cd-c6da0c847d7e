import type { ConnectionServicesDependencies } from "../../dependencies.js";
import { reshapeIntegrationManifest } from "../utils.js";
import { buildAlbacrossIntegration } from "./albacross/index.js";
import { buildAmplemarketIntegration } from "./amplemarket/index.js";
import { buildAmplitudeIntegration } from "./amplitude/index.js";
import { buildAnthropicIntegration } from "./anthropic/index.js";
import { buildAnymailFinderIntegration } from "./anymailFinder/index.js";
import { buildApolloioIntegration } from "./apolloio/index.js";
import { buildAppfiguresIntegration } from "./appfigures/index.js";
import { buildAttioIntegration } from "./attio/index.js";
import { buildAudiencecoIntegration } from "./audienceco/index.js";
import { buildBouncerIntegration } from "./bouncer/index.js";
import { buildCalendlyIntegration } from "./calendly/index.js";
import { buildCaptainDataIntegration } from "./captainData/index.js";
import { buildClearbitIntegration } from "./clearbit/index.js";
import { buildClearoutPhoneIntegration } from "./clearoutPhone/index.js";
import { buildCleon1Integration } from "./cleon1/index.js";
import { buildClosecomIntegration } from "./closecom/index.js";
import { buildCognismIntegration } from "./cognism/index.js";
import { buildContactOutIntegration } from "./contactOut/index.js";
import { buildContrastIntegration } from "./contrast/index.js";
import { buildCoresignalIntegration } from "./coresignal/index.js";
import { buildCustomerioIntegration } from "./customerio/index.js";
import { buildDatachimpIntegration } from "./datachimp/index.js";
import { buildDatagmaIntegration } from "./datagma/index.js";
import { buildDbtIntegration } from "./dbt/index.js";
import { buildDeepSeekIntegration } from "./deepSeek/index.js";
import { buildDropContactIntegration } from "./dropContact/index.js";
import { buildEnrichCrmIntegration } from "./enrichCrm/index.js";
import { buildEnrichleyIntegration } from "./enrichley/index.js";
import { buildEnrowioIntegration } from "./enrowio/index.js";
import { buildExpandiIntegration } from "./expandi/index.js";
import { buildFileIntegration } from "./file/index.js";
import { buildFindyMailIntegration } from "./findyMail/index.js";
import { buildFirecrawlIntegration } from "./firecrawl/index.js";
import { buildForagerIntegration } from "./forager/index.js";
import { buildFullEnrichIntegration } from "./fullenrich/index.js";
import { buildG2Integration } from "./g2/index.js";
import { buildGithubIntegration } from "./github/index.js";
import { buildGoogleSheetsIntegration } from "./googleSheets/index.js";
import { buildHeyReachIntegration } from "./heyReach/index.js";
import { buildHttpIntegration } from "./http/index.js";
import { buildHubspotIntegration } from "./hubspot/index.js";
import { buildHunterIntegration } from "./hunter/index.js";
import { buildIcypeasIntegration } from "./icypeas/index.js";
import { buildInstantlyIntegration } from "./instantly/index.js";
import { buildIntercomIntegration } from "./intercom/index.js";
import { buildJiraIntegration } from "./jira/index.js";
import { buildKasprIntegration } from "./kaspr/index.js";
import { buildKittIntegration } from "./kitt/index.js";
import { buildLeadMagicIntegration } from "./leadmagic/index.js";
import { buildLemlistIntegration } from "./lemlist/index.js";
import { buildLGMIntegration } from "./lgm/index.js";
import { buildLinkedinIntegration } from "./linkedin/index.js";
import { buildLinkupIntegration } from "./linkup/index.js";
import { buildLivestormIntegration } from "./livestorm/index.js";
import { buildLushaIntegration } from "./lusha/index.js";
import { buildMicrosoftTeamsIntegration } from "./microsoftTeams/index.js";
import { buildMixpanelIntegration } from "./mixpanel/index.js";
import { buildMixrankIntegration } from "./mixrank/index.js";
import { buildNetsuiteIntegration } from "./netsuite/index.js";
import { buildNetsuiteSOAPIntegration } from "./netsuiteSOAP/index.js";
import { buildNeverBounceIntegration } from "./neverBounce/index.js";
import { buildOceanioIntegration } from "./oceanio/index.js";
import { buildOctaveIntegration } from "./octave/index.js";
import { buildOneSignalIntegration } from "./oneSignal/index.js";
import { buildOpenAiIntegration } from "./openAi/index.js";
import { buildOutreachIntegration } from "./outreach/index.js";
import { buildPeopleDataLabsIntegration } from "./peopleDataLabs/index.js";
import { buildPerplexityIntegration } from "./perplexity/index.js";
import { buildPiloterrIntegration } from "./piloterr/index.js";
import { buildPipedriveIntegration } from "./pipedrive/index.js";
import { buildPredictLeadsIntegration } from "./predictLeads/index.js";
import { buildProspeoIntegration } from "./prospeo/index.js";
import { buildProxyCurlIntegration } from "./proxyCurl/index.js";
import { buildRb2bIntegration } from "./rb2b/index.js";
import { buildReverseContactIntegration } from "./reversecontact/index.js";
import { buildRiftIntegration } from "./rift/index.js";
import { buildRocketreachIntegration } from "./rocketreach/index.js";
import { buildSalesforceIntegration } from "./salesforce/index.js";
import { buildSalesloftIntegration } from "./salesloft/index.js";
import { buildSalesNavigatorIntegration } from "./salesNavigator/index.js";
import { buildScrubbyIntegration } from "./scrubby/index.js";
import { buildSendgridIntegration } from "./sendgrid/index.js";
import { buildSerperIntegration } from "./serper/index.js";
import { buildSlackIntegration } from "./slack/index.js";
import { buildSmartleadIntegration } from "./smartlead/index.js";
import { buildSnitcherIntegration } from "./snitcher/index.js";
import { buildSnovIntegration } from "./snov/index.js";
import { buildSocieteInfoIntegration } from "./societeInfo/index.js";
import { buildSqlIntegration } from "./sql/index.js";
import { buildStoreleadsIntegration } from "./storeleads/index.js";
import { buildStripeIntegration } from "./stripe/index.js";
import { buildSwarmIntegration } from "./swarm/index.js";
import { buildTheirStackIntegration } from "./theirStack/index.js";
import { buildTheneoIntegration } from "./theneo/index.js";
import { buildWaterfallIntegration } from "./waterfall/index.js";
import { buildWizaIntegration } from "./wiza/index.js";
import { buildZeroBounceIntegration } from "./zeroBounce/index.js";
import { buildZoominfoIntegration } from "./zoominfo/index.js";

export const buildNativeIntegrations = (
  dependencies: ConnectionServicesDependencies,
) => {
  return [
    buildWaterfallIntegration(dependencies),
    buildLGMIntegration(dependencies),
    buildLemlistIntegration(dependencies),
    buildHttpIntegration(dependencies),
    buildHubspotIntegration(dependencies),
    buildGoogleSheetsIntegration(dependencies),
    buildSlackIntegration(dependencies),
    buildClearbitIntegration(dependencies),
    buildSalesforceIntegration(dependencies),
    buildSendgridIntegration(dependencies),
    buildOneSignalIntegration(dependencies),
    buildLushaIntegration(dependencies),
    buildOutreachIntegration(dependencies),
    buildPipedriveIntegration(dependencies),
    buildCustomerioIntegration(dependencies),
    buildApolloioIntegration(dependencies),
    buildSalesloftIntegration(dependencies),
    buildZoominfoIntegration(dependencies),
    buildCognismIntegration(dependencies),
    buildCaptainDataIntegration(dependencies),
    buildPeopleDataLabsIntegration(dependencies),
    buildAmplitudeIntegration(dependencies),
    buildSnovIntegration(dependencies),
    buildNeverBounceIntegration(dependencies),
    buildZeroBounceIntegration(dependencies),
    buildDropContactIntegration(dependencies),
    buildPredictLeadsIntegration(dependencies),
    buildHunterIntegration(dependencies),
    buildAudiencecoIntegration(dependencies),
    buildDatagmaIntegration(dependencies),
    buildInstantlyIntegration(dependencies),
    buildStoreleadsIntegration(dependencies),
    buildClosecomIntegration(dependencies),
    buildIntercomIntegration(dependencies),
    buildScrubbyIntegration(dependencies),
    buildDbtIntegration(dependencies),
    buildOpenAiIntegration(dependencies),
    buildStripeIntegration(dependencies),
    buildSqlIntegration(dependencies),
    buildMixpanelIntegration(dependencies),
    buildExpandiIntegration(dependencies),
    buildSmartleadIntegration(dependencies),
    buildSerperIntegration(dependencies),
    buildOceanioIntegration(dependencies),
    buildCoresignalIntegration(dependencies),
    buildLivestormIntegration(dependencies),
    buildNetsuiteIntegration(dependencies),
    buildProxyCurlIntegration(dependencies),
    buildContrastIntegration(dependencies),
    buildLinkedinIntegration(dependencies),
    buildFirecrawlIntegration(dependencies),
    buildProspeoIntegration(dependencies),
    buildSalesNavigatorIntegration(dependencies),
    buildFileIntegration(dependencies),
    buildKasprIntegration(dependencies),
    buildTheneoIntegration(dependencies),
    buildFullEnrichIntegration(dependencies),
    buildReverseContactIntegration(dependencies),
    buildNetsuiteSOAPIntegration(dependencies),
    buildEnrowioIntegration(dependencies),
    buildIcypeasIntegration(dependencies),
    buildRiftIntegration(dependencies),
    buildG2Integration(dependencies),
    buildRb2bIntegration(dependencies),
    buildAlbacrossIntegration(dependencies),
    buildDatachimpIntegration(dependencies),
    buildEnrichleyIntegration(dependencies),
    buildContactOutIntegration(dependencies),
    buildPiloterrIntegration(dependencies),
    buildAnthropicIntegration(dependencies),
    buildGithubIntegration(dependencies),
    buildAttioIntegration(dependencies),
    buildMicrosoftTeamsIntegration(dependencies),
    buildLinkupIntegration(dependencies),
    buildHeyReachIntegration(dependencies),
    buildSocieteInfoIntegration(dependencies),
    buildPerplexityIntegration(dependencies),
    buildDeepSeekIntegration(dependencies),
    buildKittIntegration(dependencies),
    buildMixrankIntegration(dependencies),
    buildRocketreachIntegration(dependencies),
    buildEnrichCrmIntegration(dependencies),
    buildLeadMagicIntegration(dependencies),
    buildForagerIntegration(dependencies),
    buildWizaIntegration(dependencies),
    buildAnymailFinderIntegration(dependencies),
    buildBouncerIntegration(dependencies),
    buildClearoutPhoneIntegration(dependencies),
    buildFindyMailIntegration(dependencies),
    buildTheirStackIntegration(dependencies),
    buildCleon1Integration(dependencies),
    buildSnitcherIntegration(dependencies),
    buildAppfiguresIntegration(dependencies),
    buildCalendlyIntegration(dependencies),
    buildOctaveIntegration(dependencies),
    buildAmplemarketIntegration(dependencies),
    buildSwarmIntegration(dependencies),
    buildJiraIntegration(dependencies),
  ].map((integration) => ({
    ...integration,
    manifest: reshapeIntegrationManifest(integration.manifest),
  }));
};
