import { throwUnhandledEnumValue } from "../../../../../../utils/index.js";
import { getArrayOrSingleValue } from "../../utils.js";
import type {
  FilterLocation,
  SearchCompaniesFilter,
  SearchContactsFilter,
} from "./client.js";
import type {
  CompanySearchActionConfig,
  ContactSearchActionConfig,
} from "./lusha.js";

export const reshapeContactSearchActionConfig = (
  config: ContactSearchActionConfig,
): SearchContactsFilter => {
  const filters: SearchContactsFilter = {
    companies: {
      include: {},
      exclude: {},
    },
    contacts: {
      include: {},
      exclude: {},
    },
  };

  (config.fields || []).forEach((field) => {
    switch (field.name) {
      case "seniority":
      case "departments":
      case "existingDataPoints": {
        const value = getArrayOrSingleValue(field.autocompleteValues);

        filters["contacts"][field.inclusion][field.name] = value;

        break;
      }
      case "locations": {
        const value = getArrayOrSingleValue(field.locations);

        const locations = value.map((filter) => {
          const key = filter.name;
          return {
            [key]: filter.value,
          };
        }) as FilterLocation[];

        filters["contacts"][field.inclusion]["locations"] = locations;

        break;
      }
      default: {
        throwUnhandledEnumValue(field);
      }
    }
  });

  (config.companyFields || []).forEach((field) => {
    switch (field.name) {
      case "sics": // FIXME: not working properly - error returned but correctly implemented according to the doc
      case "naics": {
        const value = getArrayOrSingleValue(field.autocompleteValues);

        filters["companies"][field.inclusion][field.name] = value;

        break;
      }
      case "names":
      case "technologies":
      case "mainIndustriesIds":
      case "subIndustriesIds":
      case "intentTopics": {
        const value = getArrayOrSingleValue(field.values);

        filters["companies"][field.inclusion][field.name] = value;

        break;
      }
      case "sizes":
      case "revenues": {
        const value = getArrayOrSingleValue(field.values);

        filters["companies"][field.inclusion][field.name] = value;

        break;
      }
      case "locations": {
        const value = getArrayOrSingleValue(field.locations);

        const locations = value.map((filter) => {
          const key = filter.name;
          return {
            [key]: filter.value,
          };
        }) as FilterLocation[];

        filters["companies"][field.inclusion]["locations"] = locations;

        break;
      }
      default: {
        throwUnhandledEnumValue(field);
      }
    }
  });

  return filters;
};

export const reshapeCompanySearchActionConfig = (
  config: CompanySearchActionConfig,
): SearchCompaniesFilter => {
  const filters: SearchCompaniesFilter = {
    companies: {
      include: {},
      exclude: {},
    },
  };

  config.fields.forEach((field) => {
    switch (field.name) {
      case "sizes":
      case "revenues": {
        const value = getArrayOrSingleValue(field.values);

        filters["companies"][field.inclusion][field.name] = value.map(
          (size) => size,
        );

        break;
      }
      case "sics":
      case "naics": {
        const value = getArrayOrSingleValue(field.autocompleteValues);

        filters["companies"][field.inclusion][field.name] = value;

        break;
      }
      case "names":
      case "technologies":
      case "mainIndustriesIds":
      case "subIndustriesIds":
      case "intentTopics": {
        const value = getArrayOrSingleValue(field.values);

        filters["companies"][field.inclusion][field.name] = value;

        break;
      }
      case "locations": {
        const value = getArrayOrSingleValue(field.locations);

        const locations = value.map((filter) => {
          const key = filter.name;
          return {
            [key]: filter.value,
          };
        }) as FilterLocation[];

        filters["companies"][field.inclusion]["locations"] = locations;

        break;
      }
      default: {
        throwUnhandledEnumValue(field);
      }
    }
  });

  return filters;
};
