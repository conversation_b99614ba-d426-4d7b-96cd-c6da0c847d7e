import {
  ConnectionJsonSchemas,
  type ConnectionTypes,
} from "../../../../index.js";
import { icon } from "./icon.js";

const MAXIMUM_MODEL_LIMIT = 1000;
const DEFAULT_MODEL_LIMIT = 100;

export const manifest: ConnectionTypes.IntegrationManifest = {
  name: "Swarm",
  slug: "swarm",
  url: "https://theswarm.com",
  description: "Access professional network data through Swarm's API",
  icon,
  color: "#ff9d57d9",
  category: "enrichment",
  subCategories: ["listBuilding"],
  autocompletes: [
    {
      slug: "listObjectProperties",
      params: {
        jsonSchema: {
          type: "object",
          properties: {
            objectType: {
              type: "string",
            },
          },
          required: ["objectType"],
        },
      },
    },
  ],
  dynamicSchemas: [],
  connector: {
    rateLimit: {
      unit: "minute",
      max: 30,
    },
    config: {
      jsonSchema: {
        type: "object",
        properties: {
          apiKey: {
            title: "API Key",
            $ref: "#/definitions/encryption",
          },
        },
        required: ["apiKey"],
      },
      uiSchema: {
        apiKey: {
          "ui:widget": "EncryptionWidget",
        },
      },
    },
  },
  actions: {
    search: {
      name: "Search",
      description: "Search Profiles based on their attributes",
      config: {
        jsonSchema: {
          type: "object",
          properties: {
            objectType: {
              title: "Object Type",
              type: "string",
              oneOf: [
                {
                  const: "profile",
                  title: "Profile",
                },
              ],
              default: "profile",
            },
            filter: ConnectionJsonSchemas.integrationFilter,
            limit: {
              type: "number",
              minimum: 1,
              maximum: MAXIMUM_MODEL_LIMIT,
              default: DEFAULT_MODEL_LIMIT,
              title: "Limit",
              description: `Maximum number of records you want to retrieve (default: ${DEFAULT_MODEL_LIMIT}, maximum: ${MAXIMUM_MODEL_LIMIT})`,
            },
          },
          required: ["objectType", "filter"],
        },
        uiSchema: {
          filter: {
            "ui:widget": "IntegrationFilterWidget",
            "ui:options": {
              slug: "listObjectProperties",
              params: {
                objectType: "profile",
                mode: "filter",
              },
            },
          },
        },
      },
    },
  },
  model: {
    slug: {
      jsonSchema: {
        title: "Object type",
        type: "string",
        oneOf: [
          {
            const: "profile",
            title: "Profile",
            description: "Search for profiles in Swarm's network mapper",
          },
        ],
        default: "profile",
      },
      uiSchema: {},
    },
    variants: [
      {
        condition: { slugs: ["profile"] },
        config: {
          jsonSchema: {
            type: "object",
            properties: {
              filter: ConnectionJsonSchemas.integrationFilter,
              mappingCompanyWebsite: {
                $ref: "#/definitions/expression",
                title: "Mapping company website",
                description:
                  "Expression to map the company website. If not provided, the company website will be used as is.",
              },
              limit: {
                type: "number",
                title: "Limit",
                minimum: 1,
                maximum: MAXIMUM_MODEL_LIMIT,
                default: DEFAULT_MODEL_LIMIT,
                description: `Maximum number of records to retrieve. Defaults to ${DEFAULT_MODEL_LIMIT} with an upper limit of ${MAXIMUM_MODEL_LIMIT} records.`,
              },
            },
            required: ["filter", "mappingCompanyWebsite"],
          },
          uiSchema: {
            filter: {
              "ui:widget": "IntegrationFilterWidget",
              "ui:options": {
                slug: "listObjectProperties",
                params: {
                  objectType: "profile",
                  mode: "filter",
                },
              },
            },
            mappingCompanyWebsite: {
              "ui:widget": "ExpressionWidget",
            },
          },
        },
        credits: {
          costs: [
            {
              config: { jsonSchema: { type: "object" } },
              cost: 1,
              type: "unit",
              unit: "item",
            },
          ],
        },
        mode: {
          kind: "fetch",
          isIncremental: false,
          minIntervalInSeconds: 60 * 60 * 24, // 1 day,
        },
        preview: "count",
      },
    ],
  },
};
