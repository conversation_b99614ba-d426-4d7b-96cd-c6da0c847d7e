import type { ConnectionTypes } from "../../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../../dependencies.js";
import { buildSwarmClient } from "../client.js";
import type { ConnectorConfig, SearchActionConfig } from "../swarm.js";
import { reshapeIntegrationFilterToSwarmQuery } from "../utils.js";

export type SearchActionPayload =
  ConnectionTypes.IntegrationActionExecutePayload<
    ConnectorConfig,
    SearchActionConfig
  >;

export type SearchActionResult = ConnectionTypes.IntegrationActionExecuteResult;

export const searchAction = async (
  _dependencies: ConnectionServicesDependencies,
  payload: SearchActionPayload,
): Promise<SearchActionResult> => {
  const { connector, config } = payload;
  const { filter, limit } = config;

  const swarmClient = buildSwarmClient({
    apiKey: connector.config.apiKey,
  });

  const query = reshapeIntegrationFilterToSwarmQuery(filter);

  const result = await swarmClient.searchProfiles({
    query,
    limit,
  });

  return {
    outcome: "executed",
    data: result.ids,
    title:
      result.totalCount === 0
        ? "❌ No profiles found"
        : `✅ ${result.totalCount} profiles found`,
  };
};
