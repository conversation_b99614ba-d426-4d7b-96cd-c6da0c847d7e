import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { buildJiraClient } from "./client.js";
import type { ConnectorConfig } from "./jira.js";

export type AuthenticatePayload =
  ConnectionTypes.IntegrationAuthenticatePayload<ConnectorConfig>;
export type AuthenticateResult = ConnectionTypes.IntegrationAuthenticateResult;

export const authenticate = async (
  _dependencies: ConnectionServicesDependencies,
  payload: AuthenticatePayload,
): Promise<AuthenticateResult> => {
  const { connector } = payload;

  const { apiKey, domain, userEmail } = connector.config;

  const jiraClient = buildJiraClient({
    domain,
    userEmail,
    apiKey,
  });

  const isAuthenticated = await jiraClient.isAuthenticated();

  if (isAuthenticated === false) {
    return {
      outcome: "error",
      reason: "unauthenticated",
    };
  }

  return {
    outcome: "success",
  };
};
