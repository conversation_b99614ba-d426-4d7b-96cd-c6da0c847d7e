import { z } from "zod";

import { ConnectionValidation } from "../../../../index.js";

export const zodConnectorConfig = z.object({
  apiKey: z.string(),
});

export type ConnectorConfig = z.infer<typeof zodConnectorConfig>;

export const zodSearchActionConfig = z.object({
  objectType: z.literal("profile"),
  filter: ConnectionValidation.integrationFilter,
  limit: z.number().optional(),
});

export type SearchActionConfig = z.infer<typeof zodSearchActionConfig>;

export const zodModelSlug = z.literal("profile");
export type ModelSlug = z.infer<typeof zodModelSlug>;

export const zodModelConfig = z.object({
  filter: ConnectionValidation.integrationFilter,
  mappingCompanyWebsite: z.string().optional(),
});

export type ModelConfig = z.infer<typeof zodModelConfig>;

export const zodModelMeta = z.object({
  offset: z.number(),
});
export type ModelMeta = z.infer<typeof zodModelMeta>;
