import { getEntries, sentenceCase } from "cargo-utils";
import { z } from "zod";

import { ConnectionUtils } from "../../../../../index.js";

const educationSchema = z.object({
  degrees: z.array(z.string()).nullable().optional(),
  end_date: z.string().nullable().optional(),
  majors: z.array(z.string()).nullable().optional(),
  school: z
    .object({
      location: z
        .object({
          name: z.string().nullable().optional(),
        })
        .nullable()
        .optional(),
      name: z.string().nullable().optional(),
      website: z.string().nullable().optional(),
    })
    .nullable()
    .optional(),
  start_date: z.string().nullable().optional(),
});

const experienceSchema = z.object({
  company: z
    .object({
      id: z.string().nullable().optional(),
      industry: z.string().nullable().optional(),
      linkedin_slugs: z.array(z.string()).nullable().optional(),
      location: z
        .object({
          continent: z.string().nullable().optional(),
          country: z.string().nullable().optional(),
          name: z.string().nullable().optional(),
        })
        .nullable()
        .optional(),
      name: z.string().nullable().optional(),
      size: z.string().nullable().optional(),
      website: z.string().nullable().optional(),
    })
    .nullable()
    .optional(),
  end_date: z.string().nullable().optional(),
  is_current: z.boolean().nullable().optional(),
  start_date: z.string().nullable().optional(),
  title: z
    .object({
      name: z.string().nullable().optional(),
    })
    .nullable()
    .optional(),
});

export const zodProfile = z.object({
  id: z.string(),
  first_name: z.string().nullable().optional(),
  last_name: z.string().nullable().optional(),
  full_name: z.string(),
  emails: z.array(z.string()).nullable().optional(),
  linkedin_usernames: z.array(z.string()).nullable().optional(),

  current_title: z.string().nullable().optional(),
  current_job_function: z.string().nullable().optional(),
  current_seniority: z.string().nullable().optional(),
  current_start_date: z.string().nullable().optional(),
  current_job_updated_at: z.string().nullable().optional(),

  current_company_id: z.string().nullable().optional(),
  current_company_name: z.string().nullable().optional(),
  current_company_website: z.string().nullable().optional(),
  current_company_industry: z.string().nullable().optional(),
  current_company_size: z.string().nullable().optional(),
  current_company_linkedin_slugs: z.array(z.string()).nullable().optional(),
  current_company_location: z.string().nullable().optional(),
  current_company_location_country: z.string().nullable().optional(),

  current_location: z.string().nullable().optional(),
  location_country: z.string().nullable().optional(),
  location_region: z.string().nullable().optional(),
  location_locality: z.string().nullable().optional(),
  location_continent: z.string().nullable().optional(),

  industry: z.string().nullable().optional(),
  skills: z.array(z.string()).nullable().optional(),
  all_time_title: z.string().nullable().optional(),
  recent_title: z.string().nullable().optional(),

  latest_role_change_at: z.string().nullable().optional(),
  latest_company_change_at: z.string().nullable().optional(),
  created_at: z.string().nullable().optional(),
  updated_at: z.string().nullable().optional(),

  education: z.array(educationSchema).nullable().optional(),
  experience: z.array(experienceSchema).nullable().optional(),

  interests: z.string().nullable().optional(),

  connections_count: z.number().nullable().optional(),
});

export type Profile = z.infer<typeof zodProfile>;

export const profileColumns = getEntries(zodProfile.shape).map(
  ([key, schema]) => ({
    slug: key,
    type: ConnectionUtils.reshapeZodSchemaToColumnType(schema),
    label: sentenceCase(key),
  }),
);
