import type { ConnectionTypes } from "../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../dependencies.js";
import { buildSwarmClient } from "./client.js";
import type { ConnectorConfig } from "./swarm.js";

export type AuthenticatePayload =
  ConnectionTypes.IntegrationAuthenticatePayload<ConnectorConfig>;
export type AuthenticateResult = ConnectionTypes.IntegrationAuthenticateResult;

export const authenticate = async (
  _dependencies: ConnectionServicesDependencies,
  payload: AuthenticatePayload,
): Promise<AuthenticateResult> => {
  const { connector } = payload;

  const { apiKey } = connector.config;

  const swarmClient = buildSwarmClient({
    apiKey,
  });

  const isAuthenticated = await swarmClient.isAuthenticated();

  if (isAuthenticated === false) {
    return {
      outcome: "error",
      reason: "unauthenticated",
    };
  }

  return {
    outcome: "success",
  };
};
