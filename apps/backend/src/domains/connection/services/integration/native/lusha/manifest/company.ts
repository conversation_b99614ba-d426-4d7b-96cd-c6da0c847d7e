import type { Schema as JsonSchema } from "jsonschema";

export const companySchema: JsonSchema = {
  type: "object",
  properties: {
    fields: {
      type: "array",
      title: "Fields",
      description:
        "Use fields to filter the companies. Check the Lusha API documentation for more information.",
      items: {
        type: "object",
        properties: {
          name: {
            title: "Name",
            type: "string",
            anyOf: [
              {
                const: "names",
                title: "Names",
                description: "Names of companies (e.g. – Apple)",
              },
              {
                const: "locations",
                title: "Locations",
                description:
                  "Company’s HQ locations (City, State, Country and Continent)",
              },
              {
                const: "technologies",
                title: "Technologies",
                description: "Technologies used by the company (e.g. – Amazon)",
              },
              {
                const: "mainIndustriesIds",
                title: "Main Industries Ids",
                description: "Main industries of the company (e.g. – 1, 2)",
              },
              {
                const: "subIndustriesIds",
                title: "Sub Industries Ids",
                description: "Sub industries of the company (e.g. – 1, 2)",
              },
              {
                const: "intentTopics",
                title: "Intent Topics",
                description: "Intent topics of the company (e.g. – 1, 2)",
              },
              {
                const: "sizes",
                title: "Sizes",
                description: "Min and Max for company size.",
              },
              {
                const: "revenues",
                title: "Revenues",
                description: "Min and Max for the company’s revenue.",
              },
              {
                const: "sics",
                title: "Sic codes",
                description: "Codes of sics (e.g. – 1011)",
              },
              {
                const: "naics",
                title: "Naics codes",
                description: "Codes of naics (e.g. – 1141)",
              },
            ],
          },
          inclusion: {
            title: "Inclusion",
            type: "string",
            oneOf: [
              { const: "include", title: "Include" },
              { const: "exclude", title: "Exclude" },
            ],
            default: "include",
          },
        },
        allOf: [
          {
            if: {
              properties: {
                name: {
                  const: "locations",
                },
              },
            },
            then: {
              properties: {
                locations: {
                  type: "array",
                  title: "Locations",
                  items: {
                    type: "object",
                    properties: {
                      name: {
                        type: "string",
                        title: "Name",
                        oneOf: [
                          {
                            const: "continent",
                            title: "Continent",
                          },
                          {
                            const: "country",
                            title: "Country",
                          },
                          {
                            const: "countryGrouping",
                            title: "Country Grouping",
                          },
                          {
                            const: "state",
                            title: "State",
                          },
                          {
                            const: "city",
                            title: "City",
                          },
                        ],
                      },
                      value: {
                        type: "string",
                        title: "Value",
                      },
                    },
                    required: ["name", "value"],
                  },
                },
              },
              required: ["locations"],
            },
          },
          {
            if: {
              properties: {
                name: {
                  anyOf: [
                    { title: "Names", const: "names" },
                    { title: "Technologies", const: "technologies" },
                    {
                      title: "Main Industries Ids",
                      const: "mainIndustriesIds",
                    },
                    { title: "Sub Industries Ids", const: "subIndustriesIds" },
                    { title: "Intent Topics", const: "intentTopics" },
                  ],
                },
              },
            },
            then: {
              properties: {
                values: {
                  $ref: "#/definitions/integrationAutocompleteMulti",
                  title: "Values",
                },
              },
              required: ["values"],
            },
          },
          {
            if: {
              properties: {
                name: {
                  anyOf: [{ const: "sics" }, { const: "naics" }],
                },
              },
            },
            then: {
              properties: {
                autocompleteValues: {
                  $ref: "#/definitions/integrationAutocompleteMulti",
                  title: "Values",
                },
              },
              required: ["autocompleteValues"],
            },
          },
          {
            if: {
              properties: {
                name: {
                  anyOf: [{ const: "sizes" }, { const: "revenues" }],
                },
              },
            },
            then: {
              properties: {
                values: {
                  type: "array",
                  title: "Values",
                  items: {
                    type: "object",
                    properties: {
                      min: {
                        type: "number",
                        title: "Min",
                      },
                      max: {
                        type: "number",
                        title: "Max",
                      },
                    },
                  },
                },
              },
              required: ["values"],
            },
          },
        ],
        required: ["name", "inclusion"],
      },
    },
  },
};
