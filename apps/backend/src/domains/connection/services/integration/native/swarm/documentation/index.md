# Swarm

## Overview

This doc outlines how to use the Swarm connector to search for professional profiles and map their network connections using the Swarm Search API and Network Mapper API.

---

## Setup

**Create connector**  
From the settings panel or from the node catalog, search for the Swarm integration and navigate to the 'create connector' view.

**Retrieve your Swarm API key**  
Sign up or log in to your Swarm account. Navigate to your account settings to find or generate your API key.

**Configure the connector**  
Paste your Swarm API key into the dedicated field in Cargo, add a name and slug for the connector, and press save.

---

## Actions

### Search Profiles

**Search for profiles in the Swarm database using flexible queries.**

- **Endpoint:** `POST https://bee.theswarm.com/v2/profiles/search`
- **Headers:**
  - `Content-Type: application/json`
  - `x-api-key: <your-api-key>`
- **Body Example:**

```json
{
  "query": {
    "match": {
      "profile_info.job_title": {
        "query": "Data Scientist"
      }
    }
  },
  "limit": 100,
  "paginationToken": "<string>",
  "stable_pagination": false
}
```

- **Response Example:**

```json
{
  "ids": ["3c90c3cc-0d44-4b50-8888-8dd25736052a"],
  "totalCount": 123,
  "paginationToken": "<string>"
}
```

- **Description:**
  - Use OpenSearch DSL to define your search criteria. Returns a list of profile IDs matching the query. Use the pagination token to fetch additional results if needed.

[See official docs](https://docs.theswarm.com/docs/endpoints/search-profiles)

---

### Network Mapper

**Find connected profiles and their connection details.**

- **Endpoint:** `POST https://bee.theswarm.com/v2/profiles/network-mapper`
- **Headers:**
  - `Content-Type: application/json`
  - `x-api-key: <your-api-key>`
- **Body Example:**

```json
{
  "query": {
    "match": {
      "profile_info.job_title": {
        "query": "Data Scientist"
      }
    }
  }
}
```

- **Response Example:**

```json
{
  "items": [
    {
      "profile": {
        "id": "3c90c3cc-0d44-4b50-8888-8dd25736052a",
        "full_name": "<string>",
        "current_title": "<string>",
        "linkedin_url": "<string>",
        "work_email": "<string>",
        "current_company_name": "<string>",
        "current_company_website": "<string>"
      },
      "connections": [
        {
          "connector_id": "3c90c3cc-0d44-4b50-8888-8dd25736052a",
          "connector_name": "<string>",
          "connector_linkedin_url": "<string>",
          "connector_current_title": "<string>",
          "connector_current_company_name": "<string>",
          "connection_strength_normalized": 123,
          "connection_strength": 123,
          "manual_strength": 123,
          "sources": [
            {
              "origin": "<string>",
              "shared_company": "<string>",
              "shared_company_id": "3c90c3cc-0d44-4b50-8888-8dd25736052a",
              "shared_company_website": "<string>",
              "shared_company_linkedin_slug": "<string>",
              "overlap_start_date": "<string>",
              "overlap_end_date": "<string>",
              "overlap_duration_months": "<string>",
              "shared_school": "<string>",
              "shared_major": "<string>",
              "graduation_year": "<string>",
              "investor": "<string>",
              "portfolio_company": "<string>"
            }
          ]
        }
      ]
    }
  ],
  "count": 123,
  "total_count": 123
}
```

- **Description:**
  - Returns a list of profiles matching your query, along with their network connections and connection details.

[See official docs](https://docs.theswarm.com/docs/endpoints/network-mapper)

---

## FAQ

**Which rate limits should I set for this connector?**  
Refer to the [Swarm API documentation](https://docs.theswarm.com/) for up-to-date rate limits and usage guidelines.
