import type { ConnectionTypes } from "../../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../../dependencies.js";
import { buildSwarmClient } from "../client.js";
import type { ConnectorConfig, ModelConfig, ModelSlug } from "../swarm.js";
import { reshapeIntegrationFilterToSwarmQuery } from "../utils.js";

export type ModelCountPayload = ConnectionTypes.IntegrationModelCountPayload<
  ConnectorConfig,
  ModelSlug,
  ModelConfig
>;

export const modelCount = async (
  _dependencies: ConnectionServicesDependencies,
  payload: ModelCountPayload,
): Promise<ConnectionTypes.IntegrationModelCountResult> => {
  const { connector, config } = payload;
  const { filter, mappingCompanyWebsite } = config;

  const swarmClient = buildSwarmClient({
    apiKey: connector.config.apiKey,
  });

  const query = reshapeIntegrationFilterToSwarmQuery(filter);

  const result = await swarmClient.searchNetworkMapper({
    query,
    mapping_company_website: mappingCompanyWebsite,
  });

  return {
    count: result.total_count,
  };
};
