import type { ConnectionTypes } from "../../../../../index.js";
import type { ConnectionServicesDependencies } from "../../../../dependencies.js";
import { buildLushaClient } from "../client.js";
import type { ConnectorConfig, SearchActionConfig } from "../lusha.js";
import { DEFAULT_SEARCH_ACTION_LIMIT } from "../lusha.js";
import {
  reshapeCompanySearchActionConfig,
  reshapeContactSearchActionConfig,
} from "../utils.js";

export type SearchActionPayload =
  ConnectionTypes.IntegrationActionExecutePayload<
    ConnectorConfig,
    SearchActionConfig
  >;

export type SearchActionResult = ConnectionTypes.IntegrationActionExecuteResult;

export const searchAction = async (
  dependencies: ConnectionServicesDependencies,
  payload: SearchActionPayload,
): Promise<SearchActionResult> => {
  const { credits } = dependencies;
  const { connector, config } = payload;
  const { limit = DEFAULT_SEARCH_ACTION_LIMIT } = config;

  const lushaClient = buildLushaClient({
    apiKey:
      connector.config === null
        ? credits.lusha.apiKey
        : connector.config.apiKey,
  });

  if (config.objectType === "company") {
    const filters = reshapeCompanySearchActionConfig(config);

    const response = await lushaClient.searchCompanies(filters, limit);

    if (response.totalResults === 0) {
      return {
        outcome: "executed",
        data: [],
        title: "❌ No companies found",
      };
    }

    return {
      outcome: "executed",
      data: response.data,
      title: `✅ ${response.totalResults} companies found`,
    };
  }

  if (config.objectType === "contact") {
    const filters = reshapeContactSearchActionConfig(config);
    const response = await lushaClient.searchContacts(filters, limit);

    if (response.totalResults === 0) {
      return {
        outcome: "executed",
        data: [],
        title: "❌ No contacts found",
      };
    }

    return {
      outcome: "executed",
      data: response.data,
      title: `✅ ${response.totalResults} contacts found`,
    };
  }

  throw new Error("Invalid object type");
};
