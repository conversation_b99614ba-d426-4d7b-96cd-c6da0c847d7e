import type { Schema as JsonSchema } from "jsonschema";

export const contactSchema: JsonSchema = {
  type: "object",
  properties: {
    fields: {
      type: "array",
      title: "Fields",
      description:
        "Use fields to filter the contacts. Check the Lusha API documentation for more information.",
      items: {
        type: "object",
        properties: {
          name: {
            title: "Name",
            type: "string",
            anyOf: [
              {
                const: "seniority",
                title: "Seniority",
                description: "Senioritis (e.g. 5 for “Manager”)",
              },
              {
                const: "departments",
                title: "Departments",
                description: "Departments (e.g. “Product”)",
              },
              {
                const: "existingDataPoints",
                title: "Existing Data Point",
                description:
                  "Filter contacts who have this data point (e.g. “work_email”)",
              },
              {
                const: "locations",
                title: "Locations",
                description:
                  "Filter contacts by location filters (Continent, Country, Country Grouping, State, City)",
              },
            ],
          },
          inclusion: {
            title: "Inclusion",
            type: "string",
            oneOf: [
              {
                const: "include",
                title: "Include",
              },
              {
                const: "exclude",
                title: "Exclude",
              },
            ],
            default: "include",
          },
        },
        allOf: [
          {
            if: {
              properties: {
                name: {
                  const: "locations",
                },
              },
            },
            then: {
              properties: {
                locations: {
                  type: "array",
                  title: "Locations",
                  items: {
                    type: "object",
                    properties: {
                      name: {
                        type: "string",
                        title: "Name",
                        oneOf: [
                          {
                            const: "continent",
                            title: "Continent",
                          },
                          {
                            const: "country",
                            title: "Country",
                          },
                          {
                            const: "countryGrouping",
                            title: "Country Grouping",
                          },
                          {
                            const: "state",
                            title: "State",
                          },
                          {
                            const: "city",
                            title: "City",
                          },
                        ],
                      },
                      value: {
                        type: "string",
                        title: "Value",
                      },
                    },
                    required: ["name", "value"],
                  },
                },
              },
              required: ["locations"],
            },
            else: {
              if: {
                properties: {
                  name: {
                    anyOf: [
                      { title: "Countries", const: "countries" },
                      { title: "Seniority", const: "seniority" },
                      { title: "Departments", const: "departments" },
                      {
                        title: "Existing Data Points",
                        const: "existingDataPoints",
                      },
                    ],
                  },
                },
              },
              then: {
                properties: {
                  autocompleteValues: {
                    $ref: "#/definitions/integrationAutocompleteMulti",
                    title: "Values",
                  },
                },
                required: ["autocompleteValues"],
              },
            },
          },
        ],
        required: ["name", "inclusion"],
      },
    },
  },
};
