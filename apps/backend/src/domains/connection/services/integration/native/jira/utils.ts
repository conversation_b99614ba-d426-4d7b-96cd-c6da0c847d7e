import type { CreateIssuePayload } from "./client.js";
import type { WriteActionConfig } from "./jira.js";

export const reshapedFields = (fields: WriteActionConfig["fields"]) => {
  return fields.reduce<CreateIssuePayload>(
    (acc, field) => {
      if (field.name === "projectKey") {
        acc.fields.project = { key: field.autocompleteValue };
      } else if (field.name === "summary") {
        acc.fields.summary = field.value;
      } else if (field.name === "issueType") {
        acc.fields.issuetype = { id: field.autocompleteValue };
      } else if (field.name === "description") {
        acc.fields.description = {
          content: [
            {
              content: [{ text: field.value, type: "text" }],
              type: "paragraph",
            },
          ],
          type: "doc",
          version: 1,
        };
      }
      return acc;
    },
    {
      fields: {
        project: { key: "" },
        issuetype: { id: "" },
        summary: "",
        description: {
          content: [
            {
              content: [{ text: "", type: "text" }],
              type: "paragraph",
            },
          ],
          type: "doc",
          version: 1,
        },
      },
    },
  );
};
