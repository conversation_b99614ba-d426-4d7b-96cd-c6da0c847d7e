import { z } from "zod";

import { zodArrayOrSingle } from "../../utils.js";

export const MIN_SEARCH_ACTION_LIMIT = 1;
export const MAX_SEARCH_ACTION_LIMIT = 500;
export const DEFAULT_SEARCH_ACTION_LIMIT = 40;

export const zodConnectorConfig = z.object({ apiKey: z.string() }).nullable();

export type ConnectorConfig = z.infer<typeof zodConnectorConfig>;

export const zodEnrichActionConfig = z.union([
  z.object({
    objectType: z.literal("person"),
    fields: z
      .array(
        z.object({
          name: z.enum([
            "firstName",
            "lastName",
            "email",
            "companyName",
            "companyDomain",
            "linkedinUrl",
          ]),
          value: z.any(),
        }),
      )
      .optional(),
    filterBy: z.enum(["emailAddresses", "phoneNumbers"]),
    refreshJobInfo: z.boolean().optional(),
  }),
  z.object({
    objectType: z.literal("company"),
    fields: z
      .array(
        z.object({
          name: z.enum(["company", "domain"]),
          value: z.any(),
        }),
      )
      .optional(),
  }),
]);

export type EnrichActionConfig = z.infer<typeof zodEnrichActionConfig>;

const companyFieldsSchema = z.array(
  z.union([
    z.object({
      name: z.enum([
        "names",
        "technologies",
        "mainIndustriesIds",
        "subIndustriesIds",
        "intentTopics",
      ]),
      inclusion: z.enum(["include", "exclude"]),
      values: zodArrayOrSingle(z.string()),
    }),
    z.object({
      name: z.literal("locations"),
      inclusion: z.enum(["include", "exclude"]),
      locations: z.array(
        z.object({
          name: z.enum([
            "continent",
            "country",
            "countryGrouping",
            "state",
            "city",
          ]),
          value: z.string(),
        }),
      ),
    }),
    z.object({
      name: z.enum(["sics", "naics"]),
      inclusion: z.enum(["include", "exclude"]),
      autocompleteValues: zodArrayOrSingle(z.string()),
    }),
    z.object({
      name: z.enum(["sizes", "revenues"]),
      inclusion: z.enum(["include", "exclude"]),
      values: z.array(z.object({ min: z.number(), max: z.number() })),
    }),
  ]),
);

const zodContactSearchActionConfig = z.object({
  objectType: z.enum(["contact"]),
  fields: z.array(
    z.union([
      z.object({
        name: z.literal("locations"),
        inclusion: z.enum(["include", "exclude"]),
        locations: z.array(
          z.object({
            name: z.enum([
              "continent",
              "country",
              "countryGrouping",
              "state",
              "city",
            ]),
            value: z.string(),
          }),
        ),
      }),
      z.object({
        name: z.enum(["seniority", "departments", "existingDataPoints"]),
        inclusion: z.enum(["include", "exclude"]),
        autocompleteValues: zodArrayOrSingle(z.string()),
      }),
    ]),
  ),
  companyFields: companyFieldsSchema.optional(),
  limit: z.number().optional(),
});

const zodCompanySearchActionConfig = z.object({
  objectType: z.enum(["company"]),
  fields: companyFieldsSchema,
  limit: z.number().optional(),
});

export const zodSearchActionConfig = z.union([
  zodContactSearchActionConfig,
  zodCompanySearchActionConfig,
]);

export type ContactSearchActionConfig = z.infer<
  typeof zodContactSearchActionConfig
>;
export type CompanySearchActionConfig = z.infer<
  typeof zodCompanySearchActionConfig
>;
export type SearchActionConfig = z.infer<typeof zodSearchActionConfig>;
