import { v4 as uuidv4 } from "uuid";

import { getRecordsCacheKey } from "../../utils.js";
import type { StorageServicesDependencies } from "../dependencies.js";
import { insert } from "./insert.js";

export type IngestPayload = {
  workspaceUuid: string;
  modelUuid: string;
  data: any;
};

export type IngestResult =
  | {
      outcome: "ingested";
      mode: "sync";
      records: Record<string, any>[];
    }
  | {
      outcome: "ingested";
      mode: "async";
      rawRecords: Record<string, unknown>[];
    }
  | { outcome: "notIngested"; reason: "recordTooLarge" };

export const ingest = async (
  dependencies: StorageServicesDependencies,
  payload: IngestPayload,
): Promise<IngestResult> => {
  const { providers, pipelines, logger } = dependencies;
  const { workspaceUuid, modelUuid, data } = payload;

  const emittedAt = new Date();

  const records = Array.isArray(data) ? data : [data];

  const recordsWithIngestId = records
    .filter(
      (record) => typeof record === "object" && Array.isArray(record) === false,
    )
    .map((record) => {
      const {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _ingest_id,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _emitted_at,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _segments,
        ...recordWithoutBlacklistedProperties
      } = record;

      return { _ingest_id: uuidv4(), ...recordWithoutBlacklistedProperties };
    });

  if (recordsWithIngestId.length === 0) {
    return { outcome: "ingested", mode: "async", rawRecords: [] };
  }

  const key = getRecordsCacheKey(modelUuid);

  await providers.cache.lists.push(key, [
    JSON.stringify({ emittedAt, record: recordsWithIngestId[0] }),
  ]);
  await providers.cache.lists.trim(key, 0, 2);

  if (pipelines.firehose !== undefined) {
    const result = await pipelines.firehose.pushRecords({
      workspaceUuid,
      modelUuid,
      records: recordsWithIngestId,
      emittedAt,
    });

    if (result.outcome === "notPushed") {
      return {
        outcome: "notIngested",
        reason: result.reason,
      };
    }

    return {
      outcome: "ingested",
      mode: "async",
      rawRecords: recordsWithIngestId,
    };
  } else {
    const result = await insert(dependencies, {
      workspaceUuid,
      modelUuid,
      records: recordsWithIngestId,
      emittedAt,
    });

    if (result.outcome === "notInserted") {
      logger.warn("Unable to insert records", { reason: result.reason });

      throw new Error("Unable to insert");
    }

    return {
      outcome: "ingested",
      mode: "sync",
      records: result.records,
    };
  }
};
