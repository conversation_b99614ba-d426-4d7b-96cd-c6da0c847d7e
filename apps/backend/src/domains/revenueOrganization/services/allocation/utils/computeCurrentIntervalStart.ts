import type { RevenueOrganizationTypes } from "../../../index.js";

const QUARTER_MONTHS = 3;

export const getLastDayOfCurrentMonth = (date: Date): number => {
  const nextMonth = new Date(date);
  nextMonth.setUTCMonth(nextMonth.getUTCMonth() + 1);
  nextMonth.setUTCDate(0);
  return nextMonth.getUTCDate();
};

export const computeCurrentIntervalStart = (
  expirationPolicy: RevenueOrganizationTypes.AllocationExpirationPolicy,
): Date => {
  const now = new Date();
  const currentIntervalStart = new Date(now);
  currentIntervalStart.setUTCMilliseconds(0);
  currentIntervalStart.setUTCSeconds(0);

  if (expirationPolicy.interval === "hourly") {
    if (expirationPolicy.minute !== undefined) {
      currentIntervalStart.setUTCMinutes(expirationPolicy.minute);
    }

    if (currentIntervalStart > now || expirationPolicy.minute === undefined) {
      currentIntervalStart.setUTCHours(currentIntervalStart.getUTCHours() - 1);
    }

    return currentIntervalStart;
  }

  if (expirationPolicy.time !== undefined) {
    const [hours, minutes] = expirationPolicy.time
      .split(":")
      .map((item) => parseInt(item, 10));

    currentIntervalStart.setUTCHours(hours !== undefined ? hours : 0, minutes);
  }

  if (expirationPolicy.interval === "daily") {
    if (currentIntervalStart > now || expirationPolicy.time === undefined) {
      currentIntervalStart.setUTCDate(currentIntervalStart.getUTCDate() - 1);
    }
  } else if (expirationPolicy.interval === "weekly") {
    if (expirationPolicy.weekDay === undefined) {
      currentIntervalStart.setUTCDate(currentIntervalStart.getUTCDate() - 7);
    } else {
      while (
        currentIntervalStart.getUTCDay() !==
        expirationPolicy.weekDay % 7 // following the JS Date API, Sunday is 0
      ) {
        currentIntervalStart.setUTCDate(currentIntervalStart.getUTCDate() - 1);
      }
    }

    if (currentIntervalStart > now) {
      currentIntervalStart.setUTCDate(currentIntervalStart.getUTCDate() - 7);
    }
  } else if (expirationPolicy.interval === "monthly") {
    if (expirationPolicy.day === undefined) {
      currentIntervalStart.setUTCMonth(currentIntervalStart.getUTCMonth() - 1);
    } else {
      currentIntervalStart.setUTCDate(expirationPolicy.day);
      if (currentIntervalStart > now) {
        currentIntervalStart.setUTCDate(1);
        currentIntervalStart.setUTCMonth(
          currentIntervalStart.getUTCMonth() - 1,
        );
        const lastDayOfCurrentMonth =
          getLastDayOfCurrentMonth(currentIntervalStart);

        currentIntervalStart.setUTCDate(
          Math.min(expirationPolicy.day, lastDayOfCurrentMonth),
        );
      }
    }
  } else if (expirationPolicy.interval === "quarterly") {
    if (
      expirationPolicy.day === undefined ||
      expirationPolicy.month === undefined
    ) {
      currentIntervalStart.setUTCMonth(
        currentIntervalStart.getUTCMonth() - QUARTER_MONTHS,
      );
    } else {
      currentIntervalStart.setUTCDate(expirationPolicy.day);
      currentIntervalStart.setUTCMonth(expirationPolicy.month - 1);

      while (currentIntervalStart > now) {
        currentIntervalStart.setUTCDate(1);
        currentIntervalStart.setUTCMonth(
          currentIntervalStart.getUTCMonth() - QUARTER_MONTHS,
        );

        const lastDayOfCurrentMonth =
          getLastDayOfCurrentMonth(currentIntervalStart);

        currentIntervalStart.setUTCDate(
          Math.min(expirationPolicy.day, lastDayOfCurrentMonth),
        );
      }

      const threeMonthsAgo = new Date();
      threeMonthsAgo.setUTCMonth(threeMonthsAgo.getUTCMonth() - QUARTER_MONTHS);

      while (currentIntervalStart < threeMonthsAgo) {
        currentIntervalStart.setUTCMonth(
          currentIntervalStart.getUTCMonth() + QUARTER_MONTHS,
        );

        const lastDayOfCurrentMonth =
          getLastDayOfCurrentMonth(currentIntervalStart);

        currentIntervalStart.setUTCDate(
          Math.min(expirationPolicy.day, lastDayOfCurrentMonth),
        );
      }
    }
  } else if (expirationPolicy.interval === "yearly") {
    if (
      expirationPolicy.day === undefined ||
      expirationPolicy.month === undefined
    ) {
      currentIntervalStart.setUTCFullYear(
        currentIntervalStart.getUTCFullYear() - 1,
      );
    } else {
      currentIntervalStart.setUTCDate(expirationPolicy.day);
      currentIntervalStart.setUTCMonth(expirationPolicy.month - 1);
      if (currentIntervalStart > now) {
        currentIntervalStart.setUTCFullYear(
          currentIntervalStart.getUTCFullYear() - 1,
        );
      }
    }
  }

  return currentIntervalStart;
};

export const computeNextIntervalStart = (
  expirationPolicy: RevenueOrganizationTypes.AllocationExpirationPolicy,
) => {
  const currentIntervalStart = computeCurrentIntervalStart(expirationPolicy);

  if (expirationPolicy.interval === "hourly") {
    currentIntervalStart.setUTCHours(currentIntervalStart.getUTCHours() + 1);
  } else if (expirationPolicy.interval === "daily") {
    currentIntervalStart.setUTCDate(currentIntervalStart.getUTCDate() + 1);
  } else if (expirationPolicy.interval === "weekly") {
    currentIntervalStart.setUTCDate(currentIntervalStart.getUTCDate() + 7);
  } else if (expirationPolicy.interval === "monthly") {
    currentIntervalStart.setUTCMonth(currentIntervalStart.getUTCMonth() + 1);
  } else if (expirationPolicy.interval === "quarterly") {
    currentIntervalStart.setUTCMonth(
      currentIntervalStart.getUTCMonth() + QUARTER_MONTHS,
    );
  } else if (expirationPolicy.interval === "yearly") {
    currentIntervalStart.setUTCFullYear(
      currentIntervalStart.getUTCFullYear() + 1,
    );
  }

  return currentIntervalStart;
};
