import type { AiPgRepositoriesDependencies } from "../dependencies.js";
import type { DocumentRow } from "./document.js";

export type RemoveAllAfterPayload = {
  workspaceUuid: string;
  createdAfter: Date;
};

export const removeAllAfter = async (
  dependencies: AiPgRepositoriesDependencies,
  payload: RemoveAllAfterPayload,
): Promise<void> => {
  const { knex, logger } = dependencies;
  const { workspaceUuid, createdAfter } = payload;

  try {
    await knex
      .withSchema("ai")
      .table<DocumentRow>("documents")
      .update({ deleted_at: new Date() })
      .where({
        workspace_uuid: workspaceUuid,
        deleted_at: null,
      })
      .andWhere("created_at", ">", createdAfter);
  } catch (error) {
    logger.error("Failed to remove documents after date", { error, payload });

    throw error;
  }
};
