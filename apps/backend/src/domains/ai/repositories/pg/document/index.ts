import type { AiTypes } from "../../../index.js";
import type { AiPgRepositoriesDependencies } from "../dependencies.js";
import type { CreatePayload } from "./create.js";
import { create } from "./create.js";
import type { GetPayload } from "./get.js";
import { get } from "./get.js";
import type { ListPayload } from "./list.js";
import { list } from "./list.js";
import type { RemovePayload } from "./remove.js";
import { remove } from "./remove.js";
import type { RemoveAllAfterPayload } from "./removeAllAfter.js";
import { removeAllAfter } from "./removeAllAfter.js";

export type DocumentPgRepository = {
  get: (payload: GetPayload) => Promise<AiTypes.Document | undefined>;
  create: (payload: CreatePayload) => Promise<AiTypes.Document>;
  list: (payload: ListPayload) => Promise<AiTypes.Document[]>;
  remove: (payload: RemovePayload) => Promise<AiTypes.Document | undefined>;
  removeAllAfter: (payload: RemoveAllAfterPayload) => Promise<void>;
};

export const buildDocumentPgRepository = (
  dependencies: AiPgRepositoriesDependencies,
): DocumentPgRepository => ({
  get: (payload) => get(dependencies, payload),
  create: (payload) => create(dependencies, payload),
  list: (payload) => list(dependencies, payload),
  remove: (payload) => remove(dependencies, payload),
  removeAllAfter: (payload) => removeAllAfter(dependencies, payload),
});
