import type { AiTypes } from "../../../index.js";
import type { AiPgRepositoriesDependencies } from "../dependencies.js";
import type { DocumentRow } from "./document.js";
import { zodDocumentRow } from "./document.js";
import { reshapeDocumentRow } from "./reshape.js";

export type RemovePayload = {
  uuid: string;
  workspaceUuid: string;
};

export const remove = async (
  dependencies: AiPgRepositoriesDependencies,
  payload: RemovePayload,
): Promise<AiTypes.Document | undefined> => {
  const { knex, logger } = dependencies;
  const { uuid, workspaceUuid } = payload;

  try {
    const [row] = await knex
      .withSchema("ai")
      .table<DocumentRow>("documents")
      .update({ deleted_at: new Date() })
      .where({
        uuid,
        workspace_uuid: workspaceUuid,
        deleted_at: null,
      })
      .returning("*");

    if (row === undefined) {
      return undefined;
    }

    const documentRow = zodDocumentRow.parse(row);

    return reshapeDocumentRow(documentRow);
  } catch (error) {
    logger.error("Failed to remove document", { error });

    throw error;
  }
};
