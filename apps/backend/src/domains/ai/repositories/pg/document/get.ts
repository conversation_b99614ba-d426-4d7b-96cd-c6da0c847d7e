import type { AiTypes } from "../../../index.js";
import type { AiPgRepositoriesDependencies } from "../dependencies.js";
import type { DocumentRow } from "./document.js";
import { zodDocumentRow } from "./document.js";
import { reshapeDocumentRow } from "./reshape.js";

export type GetPayload = {
  workspaceUuid: string;
  uuid: string;
};

export const get = async (
  dependencies: AiPgRepositoriesDependencies,
  payload: GetPayload,
): Promise<AiTypes.Document | undefined> => {
  const { knex, logger } = dependencies;
  const { uuid, workspaceUuid } = payload;

  try {
    const row = await knex
      .withSchema("ai")
      .table<DocumentRow>("documents")
      .where({
        uuid,
        workspace_uuid: workspaceUuid,
        deleted_at: null,
      })
      .orderBy("created_at", "desc")
      .first("*");

    if (row === undefined) {
      return undefined;
    }

    const documentRow = zodDocumentRow.parse(row);

    return reshapeDocumentRow(documentRow);
  } catch (error) {
    logger.error("Failed to get document", { error });

    throw error;
  }
};
