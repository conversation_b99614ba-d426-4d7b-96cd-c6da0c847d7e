import { z } from "zod";

export const zodDocumentRow = z.object({
  uuid: z.string(),
  workspace_uuid: z.string(),
  user_uuid: z.string(),
  title: z.string().nullable(),
  content: z.string(),
  kind: z.enum(["text", "code", "image", "sheet"]),
  created_at: z.date(),
  deleted_at: z.date().nullable(),
});

export const zodDocumentRows = z.array(zodDocumentRow);

export type DocumentRow = z.infer<typeof zodDocumentRow>;
