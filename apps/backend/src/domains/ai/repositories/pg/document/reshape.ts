import type { AiTypes } from "../../../index.js";
import type { DocumentRow } from "./document.js";

export const reshapeDocumentRow = (row: DocumentRow): AiTypes.Document => {
  return {
    uuid: row.uuid,
    workspaceUuid: row.workspace_uuid,
    userUuid: row.user_uuid,
    title: row.title,
    content: row.content,
    kind: row.kind,
    createdAt: row.created_at,
    deletedAt: row.deleted_at,
  };
};
