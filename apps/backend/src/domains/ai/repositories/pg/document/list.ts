import type { AiTypes } from "../../../index.js";
import type { AiPgRepositoriesDependencies } from "../dependencies.js";
import { zodDocumentRows } from "./document.js";
import { reshapeDocumentRow } from "./reshape.js";

export type ListPayload = {
  workspaceUuid: string;
  uuid: string;
  limit?: number;
  offset?: number;
};

export const list = async (
  dependencies: AiPgRepositoriesDependencies,
  payload: ListPayload,
): Promise<AiTypes.Document[]> => {
  const { logger, knex } = dependencies;
  const { workspaceUuid, uuid, limit, offset } = payload;

  try {
    const query = knex.withSchema("ai").table("documents").where({
      workspace_uuid: workspaceUuid,
      uuid,
      deleted_at: null,
    });

    if (typeof limit === "number") {
      query.limit(limit);
    }

    if (typeof offset === "number") {
      query.offset(offset);
    }

    const rows = await query.orderBy("created_at", "desc");

    const documentRows = zodDocumentRows.parse(rows);

    return documentRows.map(reshapeDocumentRow);
  } catch (error) {
    logger.error("Failed to list documents", { payload, error });

    throw error;
  }
};
