import type { AiDataStreamWriter } from "../../../../providers/ai/index.js";
import type { AiTypes } from "../../index.js";

export type OnCreatePayload = {
  id: string;
  title: string;
  workspaceUuid: string;
  userUuid: string;
  dataStream: AiDataStreamWriter;
};

export type OnUpdatePayload = {
  document: AiTypes.Document;
  description: string;
  dataStream: AiDataStreamWriter;
};

export type AiArtifactsDocumentHandler<T extends AiTypes.DocumentKind> = {
  kind: T;
  onCreate: (payload: OnCreatePayload) => Promise<string>;
  onUpdate: (payload: OnUpdatePayload) => Promise<string>;
};
