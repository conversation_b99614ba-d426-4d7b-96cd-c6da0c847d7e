import type { AiTypes } from "../index.js";
import type { AiArtifactsDependencies } from "./dependencies.js";
import { buildCodeDocumentHandler } from "./document/code.js";
import type { AiArtifactsDocumentHandler } from "./document/document.js";
import { buildImageDocumentHandler } from "./document/image.js";
import { buildSheetDocumentHandler } from "./document/sheet.js";
import { buildTextDocumentHandler } from "./document/text.js";

export type AiArtifacts = {
  document: Record<
    AiTypes.DocumentKind,
    {
      handler: AiArtifactsDocumentHandler<AiTypes.DocumentKind>;
    }
  >;
};

export const buildAiArtifacts = (
  dependencies: AiArtifactsDependencies,
): AiArtifacts => {
  const { repositories } = dependencies;

  const handlerWrapper = <T extends AiTypes.DocumentKind>(
    handler: AiArtifactsDocumentHandler<T>,
  ): AiArtifactsDocumentHandler<T> => {
    return {
      kind: handler.kind,
      onCreate: async (payload) => {
        const content = await handler.onCreate(payload);

        await repositories.pg.document.create({
          uuid: payload.id,
          title: payload.title,
          content,
          kind: handler.kind,
          workspaceUuid: payload.workspaceUuid,
          userUuid: payload.userUuid,
        });

        return content;
      },
      onUpdate: async (payload) => {
        const draftContent = await handler.onUpdate(payload);

        await repositories.pg.document.create({
          uuid: payload.document.uuid,
          title: payload.document.title,
          content: draftContent,
          kind: handler.kind,
          workspaceUuid: payload.document.workspaceUuid,
          userUuid: payload.document.userUuid,
        });

        return draftContent;
      },
    };
  };

  return {
    document: {
      text: {
        handler: handlerWrapper(buildTextDocumentHandler(dependencies)),
      },
      image: {
        handler: handlerWrapper(buildImageDocumentHandler(dependencies)),
      },
      code: {
        handler: handlerWrapper(buildCodeDocumentHandler(dependencies)),
      },
      sheet: {
        handler: handlerWrapper(buildSheetDocumentHandler(dependencies)),
      },
    },
  };
};
