import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";
import type { CreatePayload, CreateResult } from "./create.js";
import { create } from "./create.js";
import type { GetPayload } from "./get.js";
import { get } from "./get.js";
import type { ListPayload } from "./list.js";
import { list } from "./list.js";
import type { RemovePayload, RemoveResult } from "./remove.js";
import { remove } from "./remove.js";

export type DocumentService = {
  create: (payload: CreatePayload) => Promise<CreateResult>;
  get: (payload: GetPayload) => Promise<AiTypes.Document | undefined>;
  list: (payload: ListPayload) => Promise<AiTypes.Document[]>;
  remove: (payload: RemovePayload) => Promise<RemoveResult>;
};

export const buildDocumentService = (
  dependencies: AiServicesDependencies,
): DocumentService => {
  return {
    create: (payload) => create(dependencies, payload),
    get: (payload) => get(dependencies, payload),
    list: (payload) => list(dependencies, payload),
    remove: (payload) => remove(dependencies, payload),
  };
};
