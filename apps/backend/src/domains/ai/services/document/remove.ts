import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";

export type RemovePayload = {
  workspaceUuid: string;
  uuid: string;
};

export type RemoveResult =
  | {
      outcome: "removed";
      document: AiTypes.Document;
    }
  | {
      outcome: "notRemoved";
      reason: "documentNotFound";
    };

export const remove = async (
  dependencies: AiServicesDependencies,
  payload: RemovePayload,
): Promise<RemoveResult> => {
  const { repositories } = dependencies;
  const { uuid, workspaceUuid } = payload;

  // First, get the document to find its createdAt timestamp
  const document = await repositories.pg.document.get({
    uuid,
    workspaceUuid,
  });

  if (document === undefined) {
    return {
      outcome: "notRemoved",
      reason: "documentNotFound",
    };
  }

  // Reset by removing all documents where createdAt > document.createdAt
  await repositories.pg.document.removeAllAfter({
    workspaceUuid,
    createdAfter: document.createdAt,
  });

  // Finally, remove the original document
  const removedDocument = await repositories.pg.document.remove({
    uuid,
    workspaceUuid,
  });

  return {
    outcome: "removed",
    document: removedDocument!,
  };
};
