import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";

export type RemovePayload = {
  workspaceUuid: string;
  uuid: string;
};

export type RemoveResult =
  | {
      outcome: "removed";
      document: AiTypes.Document;
    }
  | {
      outcome: "notRemoved";
      reason: "documentNotFound";
    };

export const remove = async (
  dependencies: AiServicesDependencies,
  payload: RemovePayload,
): Promise<RemoveResult> => {
  const { repositories } = dependencies;
  const { uuid, workspaceUuid } = payload;

  const document = await repositories.pg.document.remove({
    uuid,
    workspaceUuid,
  });

  if (document === undefined) {
    return {
      outcome: "notRemoved",
      reason: "documentNotFound",
    };
  }

  return {
    outcome: "removed",
    document,
  };
};
