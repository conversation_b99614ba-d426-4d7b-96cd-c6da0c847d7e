import type { AiTypes } from "../../index.js";
import type { AiServicesDependencies } from "../dependencies.js";

export type ResetPayload = {
  workspaceUuid: string;
  uuid: string;
};

export type ResetResult =
  | {
      outcome: "reset";
      document: AiTypes.Document;
    }
  | {
      outcome: "notReset";
      reason: "documentNotFound";
    };

export const reset = async (
  dependencies: AiServicesDependencies,
  payload: ResetPayload,
): Promise<ResetResult> => {
  const { repositories } = dependencies;
  const { uuid, workspaceUuid } = payload;

  const document = await repositories.pg.document.get({
    uuid,
    workspaceUuid,
  });

  if (document === undefined) {
    return {
      outcome: "notReset",
      reason: "documentNotFound",
    };
  }

  await repositories.pg.document.removeAllAfter({
    workspaceUuid,
    createdAfter: document.createdAt,
  });

  return {
    outcome: "reset",
    document,
  };
};
