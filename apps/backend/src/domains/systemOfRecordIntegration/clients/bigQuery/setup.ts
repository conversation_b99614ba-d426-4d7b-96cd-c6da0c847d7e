import type { PlainTextEncryptionValue } from "../../../../utils/index.js";
import type { SystemOfRecordIntegrationTypes } from "../../index.js";
import { columnTypeToBigQueryType } from "./columns.js";
import type { BigQueryProvider } from "./provider.js";
import { buildReferences } from "./references.js";

export const setup = async (
  provider: BigQueryProvider,
  projectId: string,
  location: string,
  config: PlainTextEncryptionValue<SystemOfRecordIntegrationTypes.BigQueryConfig>,
): Promise<SystemOfRecordIntegrationTypes.SetupResult> => {
  const references = buildReferences({ ...config, projectId });
  const computationSchema = references.computation.getSchema();

  try {
    if (computationSchema !== undefined) {
      await provider.execute({
        query: `create schema \`${computationSchema.database}.${computationSchema.name}\` options(location="${location}")`,
      });
    }

    const streamsTable = references.computation.streams.getTable();

    await provider.execute({
      query: `
        create table \`${streamsTable.database}.${streamsTable.schema}.${streamsTable.name}\` (
          record JSON
        )
      `,
    });

    const activitiesReferences = references.computation.activities;

    const activitiesTable = activitiesReferences.getTable();
    const activitiesColumns = activitiesReferences.getColumns();

    await provider.execute({
      query: `
        create table \`${activitiesTable.database}.${activitiesTable.schema}.${
          activitiesTable.name
        }\` (
          ${activitiesColumns
            .map(
              (column) =>
                `\`${column.slug}\` ${columnTypeToBigQueryType.get(column.type)}`,
            )
            .join(",\n")}
        )
        partition by date(\`_time\`)
      `,
    });

    return {
      outcome: "setup",
      config: {
        ...config,
        stringifiedServiceAccount: {
          type: "encryption",
          value: config.stringifiedServiceAccount,
          isEncrypted: false,
        },
      },
    };
  } catch (e: any) {
    return {
      outcome: "notSetup",
      errorMessage: e.message,
    };
  }
};
