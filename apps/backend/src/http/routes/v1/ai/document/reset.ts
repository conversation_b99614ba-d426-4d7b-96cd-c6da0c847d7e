import type { Response } from "express";
import { z } from "zod";

import type { AuthRequest } from "../../../../middlewares/auth.js";
import type { Dependencies } from "./index.js";

const zodResetDocumentParams = z.object({
  uuid: z.string(),
});

export const buildResetController = ({ aiServices }: Dependencies) => {
  const controller = async (req: AuthRequest, res: Response): Promise<void> => {
    const { uuid } = zodResetDocumentParams.parse(req.params);

    if (uuid === undefined) {
      res.status(400).json({ errorMessage: "uuid is required" });
      return;
    }

    const result = await aiServices.document.reset({
      workspaceUuid: req.auth!.selectedWorkspaceUuid,
      uuid,
    });

    if (result.outcome === "notReset") {
      res.status(404).json({
        reason: result.reason,
      });

      return;
    }

    res.status(200).json({
      document: result.document,
    });
  };

  return controller;
};
