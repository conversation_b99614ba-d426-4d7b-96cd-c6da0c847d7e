import { Router } from "express";
import asyncHandler from "express-async-handler";

import type { AiServices } from "../../../../../domains/ai/index.js";
import { buildCreateController } from "./create.js";
import { buildGetController } from "./get.js";
import { buildListController } from "./list.js";
import { buildResetController } from "./reset.js";

export interface Dependencies {
  aiServices: AiServices;
}

export const buildDocumentRouter = (dependencies: Dependencies) => {
  const documentRoute = Router();

  documentRoute.post("/", asyncHandler(buildCreateController(dependencies)));
  documentRoute.get("/list", asyncHandler(buildListController(dependencies)));
  documentRoute.get("/:uuid", asyncHandler(buildGetController(dependencies)));
  documentRoute.post(
    "/:uuid/reset",
    asyncHand<PERSON>(buildResetController(dependencies)),
  );

  return documentRoute;
};
