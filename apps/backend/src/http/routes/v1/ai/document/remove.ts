import type { Response } from "express";
import { z } from "zod";

import type { AuthRequest } from "../../../../middlewares/auth.js";
import type { Dependencies } from "./index.js";

const zodRemoveDocumentParams = z.object({
  uuid: z.string(),
});

export const buildRemoveController = ({ aiServices }: Dependencies) => {
  const controller = async (req: AuthRequest, res: Response): Promise<void> => {
    const { uuid } = zodRemoveDocumentParams.parse(req.params);

    if (uuid === undefined) {
      res.status(400).json({ errorMessage: "uuid is required" });
      return;
    }

    try {
      const result = await aiServices.document.remove({
        workspaceUuid: req.auth!.selectedWorkspaceUuid,
        uuid,
      });

      if (result.outcome === "notRemoved") {
        res.status(404).json({
          error: "Document not found",
        });
        return;
      }

      // The remove operation now performs a reset - removing the document and all documents created after it
      res.status(200).json({
        message:
          "Document and all subsequent documents have been removed (reset operation)",
        document: result.document,
      });
    } catch (_error) {
      res.status(500).json({
        error: "Failed to remove document",
      });
    }
  };

  return controller;
};
