import type { Request, Response } from "express";
import { z } from "zod";

import type { Dependencies } from "./index.js";

const zodRemoveDocumentParams = z.object({
  uuid: z.string(),
});

export const buildRemoveController =
  (dependencies: Dependencies) =>
  async (request: Request, response: Response): Promise<void> => {
    const { aiServices } = dependencies;
    const { workspaceUuid } = request.workspace;

    const { uuid } = zodRemoveDocumentParams.parse(request.params);

    const result = await aiServices.document.remove({
      workspaceUuid,
      uuid,
    });

    if (result.outcome === "notRemoved") {
      response.status(404).json({
        error: "Document not found",
      });
      return;
    }

    response.status(200).json({
      document: result.document,
    });
  };
