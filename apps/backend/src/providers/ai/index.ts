import { createAnthropic } from "@ai-sdk/anthropic";
import { createOpenAI } from "@ai-sdk/openai";
import type { LanguageModelV1, Provider, Tool } from "ai";
import {
  appendResponseMessages,
  convertToCoreMessages,
  createDataStream,
  createDataStreamResponse,
  experimental_createMCPClient,
  experimental_generateImage,
  generateObject,
  generateText,
  pipeDataStreamToResponse,
  smoothStream,
  streamObject,
  streamText,
} from "ai";

export type {
  DataStreamWriter as AiDataStreamWriter,
  Tool as AiTool,
  ToolSet as AiToolSet,
} from "ai";
export {
  type Message as AiMessage,
  type StreamTextResult as AiStreamTextResult,
  jsonSchema,
} from "ai";

import { z } from "zod";

import type { Logger } from "../logger/index.js";

export type VercelAiProviderType = "openai" | "anthropic";

export type VercelAiProvider = Provider & {
  responses?: (modelId: string) => LanguageModelV1;
};

export type VercelAiClientDependencies = {
  logger: Logger;
  config: {
    openai: {
      apiKey: string;
    };
    anthropic: {
      apiKey: string;
    };
  };
};

export type VercelAiClient = {
  getProvider: (
    type: VercelAiProviderType,
    config?: { apiKey: string },
  ) => VercelAiProvider;
  generateText: typeof generateText;
  generateObject: typeof generateObject;
  generateImage: typeof experimental_generateImage;
  streamText: typeof streamText;
  streamObject: typeof streamObject;
  createDataStream: typeof createDataStream;
  createDataStreamResponse: typeof createDataStreamResponse;
  pipeDataStreamToResponse: typeof pipeDataStreamToResponse;
  convertToCoreMessages: typeof convertToCoreMessages;
  appendResponseMessages: typeof appendResponseMessages;
  smoothStream: typeof smoothStream;
  createMCPClient: typeof experimental_createMCPClient;
  tools: {
    webSearch: (payload?: {
      searchContextSize?: "low" | "medium" | "high";
      userLocation?: {
        type?: "approximate";
        city?: string;
        region?: string;
        country?: string;
        timezone?: string;
      };
    }) => Tool;
  };
};

export const buildVercelAiClient = (
  dependencies: VercelAiClientDependencies,
): VercelAiClient => {
  const openAiProvider = createOpenAI({
    apiKey: dependencies.config.openai.apiKey,
  });

  return {
    getProvider: (type, config) => {
      if (type === "openai") {
        return createOpenAI({
          apiKey:
            config !== undefined
              ? config.apiKey
              : dependencies.config.openai.apiKey,
          compatibility: "strict",
        });
      }

      if (type === "anthropic") {
        return createAnthropic({
          apiKey:
            config !== undefined
              ? config.apiKey
              : dependencies.config.openai.apiKey,
        }) as VercelAiProvider;
      }

      throw new Error("Invalid provider");
    },
    generateText,
    generateObject,
    generateImage: experimental_generateImage,
    streamText,
    streamObject,
    createDataStream,
    createDataStreamResponse,
    pipeDataStreamToResponse,
    convertToCoreMessages,
    appendResponseMessages,
    smoothStream,
    createMCPClient: experimental_createMCPClient,
    tools: {
      webSearch: (payload) => {
        const tool = openAiProvider.tools.webSearchPreview(payload);

        return {
          id: tool.id,
          parameters: z.object({ prompt: z.string() }),
          execute: async (params) => {
            const result = await generateText({
              model: openAiProvider.responses("gpt-4o-mini"),
              prompt: params.prompt,
              tools: {
                web_search_preview: tool,
              },
              toolChoice: { type: "tool", toolName: "web_search_preview" },
            });

            return { text: result.text, sources: result.sources };
          },
        };
      },
    },
  };
};
