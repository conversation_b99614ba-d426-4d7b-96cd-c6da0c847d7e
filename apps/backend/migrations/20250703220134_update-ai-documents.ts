import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // First, drop the existing primary key constraint
    await trx.raw(`
      ALTER TABLE ai.documents DROP CONSTRAINT documents_pkey;
    `);

    // Remove the updated_at column
    await trx.schema.withSchema("ai").alterTable("documents", (table) => {
      table.dropColumn("updated_at");
    });

    // Add the new composite primary key (uuid + created_at)
    await trx.raw(`
      ALTER TABLE ai.documents ADD CONSTRAINT documents_pkey PRIMARY KEY (uuid, created_at);
    `);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // Drop the composite primary key
    await trx.raw(`
      ALTER TABLE ai.documents DROP CONSTRAINT documents_pkey;
    `);

    // Add back the updated_at column
    await trx.schema.withSchema("ai").alterTable("documents", (table) => {
      table.timestamp("updated_at").notNullable().defaultTo(trx.fn.now());
    });

    // Restore the original primary key (just uuid)
    await trx.raw(`
      ALTER TABLE ai.documents ADD CONSTRAINT documents_pkey PRIMARY KEY (uuid);
    `);
  });
}
