import type {
  ActiveElement as ActiveChartJsElement,
  Chart as ChartJs,
  ChartData,
  ChartEvent,
  ChartOptions as ChartJSOptions,
  ChartTypeRegistry,
} from "chart.js/auto";
import ChartJS from "chart.js/auto";
import React from "react";

export type ChartOptions = Omit<
  ChartJSOptions,
  "hoverBackgroundColor" | "onHover"
> & {
  hoverBackgroundColor?: string;
  onMouseHover?: (
    event: ChartEvent,
    elements: ActiveChartJsElement[],
    chart: ChartJS,
  ) => void;
  onMouseLeave?: (data: ChartData) => void;
};

type Props = {
  data: ChartData;
  options: ChartOptions;
  type: keyof ChartTypeRegistry;
  id?: string;
};

type ActiveElement = {
  x: number;
  width?: number;
  y: number;
  base?: number;
};

type HoverEventParams = {
  hoverBackgroundColor: string;
};

const TICKS_THRESHOLD = 15.7;

const chartKeyMap = new Map<string, boolean>();

export const Chart: React.FC<Props> = (props) => {
  const { options, type, data, id } = props;
  const chartRef = React.useRef<HTMLCanvasElement | null>(null);
  const chartInstance = React.useRef<ChartJS | null>(null);

  const isAnimationEnabled = React.useMemo(() => {
    if (id === undefined) {
      return false;
    }

    return chartKeyMap.get(id) !== true;
  }, [id]);

  React.useEffect(() => {
    if (isAnimationEnabled === true && id !== undefined) {
      chartKeyMap.set(id, true);
    }
  }, [isAnimationEnabled, id]);

  React.useEffect(() => {
    const chartElement = chartRef.current;

    const loadChart = async () => {
      if (chartElement === null) {
        return;
      }

      const context2D = chartElement.getContext("2d");

      if (context2D === null) {
        return;
      }

      if (chartInstance.current !== null) {
        chartInstance.current.destroy();
      }

      const { hoverBackgroundColor, ...otherOptions } = options;

      const hoverParams: HoverEventParams = {
        hoverBackgroundColor: hoverBackgroundColor || "",
      };

      ChartJS.unregister(hoverEvent(hoverParams));

      ChartJS.register(hoverEvent(hoverParams));

      chartInstance.current = new ChartJS(context2D, {
        type,
        data,
        options: {
          ...otherOptions,
          interaction: {
            mode: "index",
            axis: "x",
            intersect: false,
          },
          animation:
            isAnimationEnabled === true
              ? {
                  duration: 300,
                  delay: (context) => {
                    const datasetIndex = context.datasetIndex || 0;
                    const dataIndex = context.dataIndex || 0;
                    return datasetIndex * 20 + dataIndex * 10;
                  },
                }
              : false,
          onHover: (event, elements, chart) => {
            if (options.onMouseHover == undefined) {
              return;
            }
            options.onMouseHover(event, elements, chart);
          },
          datasets: {
            bar: {
              hoverBackgroundColor: () => {
                return undefined;
              },
              hoverBorderColor: () => {
                return undefined;
              },
            },
          },
        },
      });
    };

    if (chartElement !== null) {
      chartElement.addEventListener("mouseleave", () => {
        if (options.onMouseLeave !== undefined) {
          options.onMouseLeave(data);
        }
      });
    }

    loadChart();

    return () => {
      if (chartInstance.current !== null) {
        chartInstance.current.destroy();
      }
      if (chartElement !== null) {
        chartElement.removeEventListener("mouseleave", () => {
          if (options.onMouseLeave !== undefined) {
            options.onMouseLeave(data);
          }
        });
      }
    };
  }, [data, isAnimationEnabled, options, type]);

  return <canvas ref={chartRef} />;
};

function hoverEvent(params: HoverEventParams) {
  return {
    id: "hoverEvent",
    afterDraw(chart: ChartJs) {
      const activeElements = chart.getActiveElements();
      const { ctx, chartArea } = chart;

      const firstActiveElement = activeElements[0];

      if (firstActiveElement === undefined) {
        return;
      }

      const firstPoint = firstActiveElement.element as ActiveElement;
      const { x, width = 0 } = firstPoint;

      ctx.fillStyle = params?.hoverBackgroundColor;

      const adjustedX = x - TICKS_THRESHOLD;
      const adjustedWidth = width;

      ctx.save();
      ctx.globalCompositeOperation = "destination-over";
      ctx.fillRect(
        adjustedX,
        chartArea.top,
        adjustedWidth,
        chartArea.bottom - chartArea.top,
      );
      ctx.restore();
    },
  };
}
