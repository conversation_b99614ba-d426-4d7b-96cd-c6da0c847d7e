import type { SegmentationTypes } from "../segmentation/segmentation.js";

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace AiTypes {
  export type Tool = {
    name: string;
    slug: string | null;
    description: string | null;
    isBulkAllowed: boolean;
    config: Record<string, unknown>;
  } & (
    | {
        kind: "tool";
        toolUuid: string | null;
        templateSlug: string | null;
      }
    | {
        kind: "connector";
        integrationSlug: string;
        connectorUuid: string | null;
        actionSlug: string;
      }
  );

  export type Resource = {
    name: string;
    slug: string | null;
    description: string | null;
    modelUuid: string;
    filter: SegmentationTypes.Filter | null;
    limit: number | null;
    mode: "vector" | "sql";
    prompt: string | null;
  };

  export type McpServer = {
    uuid: string;
    workspaceUuid: string;
    userUuid: string;
    name: string;
    tools: Tool[];
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
  };

  export type AgentDeployment = {
    name: string;
    description: string | null;
    type: "connector";
    connectorUuid: string | null;
    integrationSlug: string;
    config: Record<string, unknown>;
  };

  export type AgentCategory =
    | "home"
    | "research"
    | "qualification"
    | "routing"
    | "engagement";

  export type AgentIcon = {
    color: "grey" | "green" | "purple" | "yellow" | "blue" | "red";
    face: string;
  };

  export type Agent = {
    uuid: string;
    workspaceUuid: string;
    userUuid: string | null;
    name: string;
    icon: AgentIcon;
    description: string | null;
    category: AgentCategory | null;
    deployments: AgentDeployment[];
    deployedRelease: {
      uuid: string;
      userUuid: string;
      description: string | null;
      version: string | null;
      tools: Tool[];
      systemPrompt: string;
      temperature: number;
      maxSteps: number;
      provider: ReleaseProvider;
      model: string;
      deployedAt: Date | null;
      createdAt: Date;
    } | null;
    folderUuid: string | null;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
  };

  export type ReleaseStatus = "draft" | "deployed" | "archived";

  export type ReleaseProvider = "openai" | "anthropic";

  export type ReleaseSuggestedAction = {
    text: string;
  };

  export type ReleaseMcpClient = {
    name: string;
    url: string;
    headers: Record<string, string>;
    disabledToolSlugs: string[];
  };

  export type Release = {
    uuid: string;
    parentUuid: string | null;
    workspaceUuid: string;
    userUuid: string;
    agentUuid: string;
    description: string | null;
    version: string | null;
    status: ReleaseStatus;
    tools: Tool[];
    resources: Resource[];
    suggestedActions: ReleaseSuggestedAction[];
    systemPrompt: string;
    temperature: number;
    maxSteps: number;
    provider: ReleaseProvider;
    model: string;
    mcpClients: ReleaseMcpClient[];
    deployedAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
  };

  export type ChatTrigger =
    | {
        type: "connector";
        connectorUuid: string;
        meta: Record<string, unknown>;
      }
    | { type: "draft" };

  export type Chat = {
    uuid: string;
    slug: string;
    workspaceUuid: string;
    agentUuid: string;
    releaseUuid: string;
    name: string | null;
    trigger: ChatTrigger | null;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
  };

  export type MessageStatus = "pending" | "generating" | "success" | "error";

  export type MessageType =
    | "user" // Messages from the end user
    | "assistant" // Messages from the AI assistant
    | "system"; // System notifications or instructions

  export type ReasoningPart = {
    type: "reasoning";
    reasoning: string;
    details: Array<
      | {
          type: "text";
          text: string;
          signature?: string;
        }
      | {
          type: "redacted";
          data: string;
        }
    >;
  };

  export type StepStartPart = {
    type: "step-start";
  };

  export type TextPart = {
    type: "text";
    text: string;
  };

  export type ToolCallPart<
    Args extends Record<string, unknown> = Record<string, unknown>,
  > = {
    type: "tool-call";
    toolCallId: string;
    toolName: string;
    args: Args;
  };

  export type ToolResultPart<
    Result extends Record<string, unknown> = Record<string, unknown>,
  > = {
    type: "tool-result";
    toolCallId: string;
    toolName: string;
    result: Result;
    isError: boolean;
  };

  export type MessagePart =
    | ReasoningPart
    | StepStartPart
    | TextPart
    | ToolCallPart
    | ToolResultPart;

  export type MessageAttachment = {
    name?: string;
    s3Filename: string;
    contentType: string;
  };

  export type Message = {
    uuid: string;
    workspaceUuid: string;
    agentUuid: string;
    releaseUuid: string;
    chatUuid: string;
    userUuid: string | null;
    status: MessageStatus;
    errorMessage: string | null;
    type: MessageType;
    parts: MessagePart[];
    attachments: MessageAttachment[];
    creditsUsedCount: number;
    meta: Record<string, unknown> | null;
    llmUsage: {
      totalTokensCount?: number;
      promptTokensCount?: number;
      completionTokensCount?: number;
      model?: string;
    };
    temporalWorkflowId: string | null;
    createdAt: Date;
    updatedAt: Date;
    finishedAt: Date | null;
    deletedAt: Date | null;
  };

  export type Vote = {
    workspaceUuid: string;
    userUuid: string;
    chatUuid: string;
    messageUuid: string;
    isUpvoted: boolean;
    updatedAt: Date;
  };

  export type DocumentKind = "text" | "code" | "image" | "sheet";

  export type Document = {
    uuid: string;
    workspaceUuid: string;
    userUuid: string;
    title: string | null;
    content: string;
    kind: DocumentKind;
    createdAt: Date;
    deletedAt: Date | null;
  };

  export type DocumentSuggestion = {
    uuid: string;
    workspaceUuid: string;
    userUuid: string;
    documentUuid: string;
    documentCreatedAt: Date;
    originalText: string;
    suggestedText: string;
    description: string | null;
    isResolved: boolean;
    createdAt: Date;
  };
}
